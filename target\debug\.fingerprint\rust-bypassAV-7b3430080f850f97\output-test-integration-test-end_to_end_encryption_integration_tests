{"$message_type":"diagnostic","message":"unused import: `AppError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":444,"byte_end":452,"line_start":16,"line_end":16,"column_start":49,"column_end":57,"is_primary":true,"text":[{"text":"use rust_bypassAV::orchestrator::{Orchestra<PERSON>, AppError};","highlight_start":49,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":442,"byte_end":452,"line_start":16,"line_end":16,"column_start":47,"column_end":57,"is_primary":true,"text":[{"text":"use rust_bypassAV::orchestrator::{Orchestrator, AppError};","highlight_start":47,"highlight_end":57}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":429,"byte_end":430,"line_start":16,"line_end":16,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"use rust_bypassAV::orchestrator::{Orchestrator, AppError};","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":452,"byte_end":453,"line_start":16,"line_end":16,"column_start":57,"column_end":58,"is_primary":true,"text":[{"text":"use rust_bypassAV::orchestrator::{Orchestrator, AppError};","highlight_start":57,"highlight_end":58}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `AppError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\end_to_end_encryption_integration_tests.rs:16:49\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_bypassAV::orchestrator::{Orchestrator, AppError};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"arguments to this function are incorrect","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":10031,"byte_end":10069,"line_start":274,"line_end":274,"column_start":9,"column_end":47,"is_primary":false,"text":[{"text":"        \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),","highlight_start":9,"highlight_end":47}],"label":"unexpected argument #2 of type `std::string::String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":10079,"byte_end":10123,"line_start":275,"line_end":275,"column_start":9,"column_end":53,"is_primary":false,"text":[{"text":"        Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),","highlight_start":9,"highlight_end":53}],"label":"unexpected argument #3 of type `Option<std::string::String>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":10133,"byte_end":10238,"line_start":276,"line_end":279,"column_start":9,"column_end":11,"is_primary":false,"text":[{"text":"        Some(JitDecryptionConfig {","highlight_start":9,"highlight_end":35},{"text":"            chunk_size: 512,","highlight_start":1,"highlight_end":29},{"text":"            re_encrypt_inactive: true,","highlight_start":1,"highlight_end":39},{"text":"        })","highlight_start":1,"highlight_end":11}],"label":"unexpected argument #4 of type `Option<JitDecryptionConfig>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":9991,"byte_end":10123,"line_start":273,"line_end":275,"column_start":9,"column_end":53,"is_primary":false,"text":[{"text":"        EncryptionAlgorithm::Aes128Cbc,","highlight_start":9,"highlight_end":40},{"text":"        \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),","highlight_start":1,"highlight_end":48},{"text":"        Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),","highlight_start":1,"highlight_end":53}],"label":"three arguments of type `&str`, `&str`, and `&str` are missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":9948,"byte_end":9981,"line_start":272,"line_end":272,"column_start":30,"column_end":63,"is_primary":true,"text":[{"text":"    let mut crypto_manager = CryptoManager::new_with_algorithm(","highlight_start":30,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\Desktop\\rust_bypass1-5.28\\src\\core\\crypto.rs","byte_start":1127,"byte_end":1145,"line_start":31,"line_end":31,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn new_with_algorithm(","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"did you mean","code":null,"level":"help","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":9981,"byte_end":10244,"line_start":272,"line_end":280,"column_start":63,"column_end":6,"is_primary":true,"text":[{"text":"    let mut crypto_manager = CryptoManager::new_with_algorithm(","highlight_start":63,"highlight_end":64},{"text":"        EncryptionAlgorithm::Aes128Cbc,","highlight_start":1,"highlight_end":40},{"text":"        \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),","highlight_start":1,"highlight_end":48},{"text":"        Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),","highlight_start":1,"highlight_end":54},{"text":"        Some(JitDecryptionConfig {","highlight_start":1,"highlight_end":35},{"text":"            chunk_size: 512,","highlight_start":1,"highlight_end":29},{"text":"            re_encrypt_inactive: true,","highlight_start":1,"highlight_end":39},{"text":"        })","highlight_start":1,"highlight_end":11},{"text":"    ).expect(\"Failed to create JIT CryptoManager\");","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":"(/* &str */, /* &str */, /* &str */, EncryptionAlgorithm::Aes128Cbc)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: arguments to this function are incorrect\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\end_to_end_encryption_integration_tests.rs:272:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m272\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    let mut crypto_manager = CryptoManager::new_with_algorithm(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m273\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        EncryptionAlgorithm::Aes128Cbc,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m274\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #2 of type `std::string::String`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m275\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------------------------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|_________|__________________________________________|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthree arguments of type `&str`, `&str`, and `&str` are missing\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #3 of type `Option<std::string::String>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m276\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Some(JitDecryptionConfig {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m277\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            chunk_size: 512,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m278\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            re_encrypt_inactive: true,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m279\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        })\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|__________-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #4 of type `Option<JitDecryptionConfig>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\Desktop\\rust_bypass1-5.28\\src\\core\\crypto.rs:31:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m31\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_with_algorithm(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: did you mean\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m272\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let mut crypto_manager = CryptoManager::new_with_algorithm\u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m273\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         EncryptionAlgorithm::Aes128Cbc,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m274\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m275\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m276\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         Some(JitDecryptionConfig {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m277\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-             chunk_size: 512,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m278\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-             re_encrypt_inactive: true,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m279\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         })\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m280\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-     )\u001b[0m\u001b[0m.expect(\"Failed to create JIT CryptoManager\");\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m272\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let mut crypto_manager = CryptoManager::new_with_algorithm\u001b[0m\u001b[0m\u001b[38;5;10m(/* &str */, /* &str */, /* &str */, EncryptionAlgorithm::Aes128Cbc)\u001b[0m\u001b[0m.expect(\"Failed to create JIT CryptoManager\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `encrypt_for_storage` found for struct `CryptoManager` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":10337,"byte_end":10356,"line_start":282,"line_end":282,"column_start":46,"column_end":65,"is_primary":true,"text":[{"text":"    let encrypted_shellcode = crypto_manager.encrypt_for_storage(&large_shellcode)","highlight_start":46,"highlight_end":65}],"label":"method not found in `CryptoManager`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `encrypt_for_storage` found for struct `CryptoManager` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\end_to_end_encryption_integration_tests.rs:282:46\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m282\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let encrypted_shellcode = crypto_manager.encrypt_for_storage(&large_shellcode)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `CryptoManager`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"arguments to this function are incorrect","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":13750,"byte_end":13780,"line_start":372,"line_end":372,"column_start":9,"column_end":39,"is_primary":false,"text":[{"text":"        \"U2VjcmV0S2V5MTIz\".to_string(),","highlight_start":9,"highlight_end":39}],"label":"unexpected argument #2 of type `std::string::String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":13790,"byte_end":13794,"line_start":373,"line_end":373,"column_start":9,"column_end":13,"is_primary":false,"text":[{"text":"        None, None","highlight_start":9,"highlight_end":13}],"label":"unexpected argument #3 of type `Option<_>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":13796,"byte_end":13800,"line_start":373,"line_end":373,"column_start":15,"column_end":19,"is_primary":false,"text":[{"text":"        None, None","highlight_start":15,"highlight_end":19}],"label":"unexpected argument #4 of type `Option<_>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":13716,"byte_end":13794,"line_start":371,"line_end":373,"column_start":9,"column_end":13,"is_primary":false,"text":[{"text":"        EncryptionAlgorithm::Xor,","highlight_start":9,"highlight_end":34},{"text":"        \"U2VjcmV0S2V5MTIz\".to_string(),","highlight_start":1,"highlight_end":40},{"text":"        None, None","highlight_start":1,"highlight_end":13}],"label":"three arguments of type `&str`, `&str`, and `&str` are missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":13673,"byte_end":13706,"line_start":370,"line_end":370,"column_start":26,"column_end":59,"is_primary":true,"text":[{"text":"    let mut xor_crypto = CryptoManager::new_with_algorithm(","highlight_start":26,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\Desktop\\rust_bypass1-5.28\\src\\core\\crypto.rs","byte_start":1127,"byte_end":1145,"line_start":31,"line_end":31,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn new_with_algorithm(","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"did you mean","code":null,"level":"help","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":13706,"byte_end":13806,"line_start":370,"line_end":374,"column_start":59,"column_end":6,"is_primary":true,"text":[{"text":"    let mut xor_crypto = CryptoManager::new_with_algorithm(","highlight_start":59,"highlight_end":60},{"text":"        EncryptionAlgorithm::Xor,","highlight_start":1,"highlight_end":34},{"text":"        \"U2VjcmV0S2V5MTIz\".to_string(),","highlight_start":1,"highlight_end":40},{"text":"        None, None","highlight_start":1,"highlight_end":19},{"text":"    ).expect(\"Failed to create XOR manager\");","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":"(/* &str */, /* &str */, /* &str */, EncryptionAlgorithm::Xor)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: arguments to this function are incorrect\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\end_to_end_encryption_integration_tests.rs:370:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m370\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    let mut xor_crypto = CryptoManager::new_with_algorithm(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m371\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        EncryptionAlgorithm::Xor,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m372\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        \"U2VjcmV0S2V5MTIz\".to_string(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #2 of type `std::string::String`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m373\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        None, None\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #4 of type `Option<_>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|_________|__|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthree arguments of type `&str`, `&str`, and `&str` are missing\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #3 of type `Option<_>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\Desktop\\rust_bypass1-5.28\\src\\core\\crypto.rs:31:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m31\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_with_algorithm(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: did you mean\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m370\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let mut xor_crypto = CryptoManager::new_with_algorithm\u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m371\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         EncryptionAlgorithm::Xor,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m372\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         \"U2VjcmV0S2V5MTIz\".to_string(),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m373\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         None, None\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m374\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-     )\u001b[0m\u001b[0m.expect(\"Failed to create XOR manager\");\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m370\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let mut xor_crypto = CryptoManager::new_with_algorithm\u001b[0m\u001b[0m\u001b[38;5;10m(/* &str */, /* &str */, /* &str */, EncryptionAlgorithm::Xor)\u001b[0m\u001b[0m.expect(\"Failed to create XOR manager\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `encrypt_for_storage` found for struct `CryptoManager` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":13883,"byte_end":13902,"line_start":376,"line_end":376,"column_start":36,"column_end":55,"is_primary":true,"text":[{"text":"    let xor_encrypted = xor_crypto.encrypt_for_storage(&test_shellcode)","highlight_start":36,"highlight_end":55}],"label":"method not found in `CryptoManager`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `encrypt_for_storage` found for struct `CryptoManager` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\end_to_end_encryption_integration_tests.rs:376:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m376\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let xor_encrypted = xor_crypto.encrypt_for_storage(&test_shellcode)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `CryptoManager`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"arguments to this function are incorrect","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":15226,"byte_end":15264,"line_start":407,"line_end":407,"column_start":9,"column_end":47,"is_primary":false,"text":[{"text":"        \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),","highlight_start":9,"highlight_end":47}],"label":"unexpected argument #2 of type `std::string::String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":15274,"byte_end":15318,"line_start":408,"line_end":408,"column_start":9,"column_end":53,"is_primary":false,"text":[{"text":"        Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),","highlight_start":9,"highlight_end":53}],"label":"unexpected argument #3 of type `Option<std::string::String>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":15328,"byte_end":15332,"line_start":409,"line_end":409,"column_start":9,"column_end":13,"is_primary":false,"text":[{"text":"        None","highlight_start":9,"highlight_end":13}],"label":"unexpected argument #4 of type `Option<_>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":15186,"byte_end":15318,"line_start":406,"line_end":408,"column_start":9,"column_end":53,"is_primary":false,"text":[{"text":"        EncryptionAlgorithm::Aes128Cbc,","highlight_start":9,"highlight_end":40},{"text":"        \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),","highlight_start":1,"highlight_end":48},{"text":"        Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),","highlight_start":1,"highlight_end":53}],"label":"three arguments of type `&str`, `&str`, and `&str` are missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":15143,"byte_end":15176,"line_start":405,"line_end":405,"column_start":29,"column_end":62,"is_primary":true,"text":[{"text":"    let mut aes128_crypto = CryptoManager::new_with_algorithm(","highlight_start":29,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\Desktop\\rust_bypass1-5.28\\src\\core\\crypto.rs","byte_start":1127,"byte_end":1145,"line_start":31,"line_end":31,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn new_with_algorithm(","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"did you mean","code":null,"level":"help","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":15176,"byte_end":15338,"line_start":405,"line_end":410,"column_start":62,"column_end":6,"is_primary":true,"text":[{"text":"    let mut aes128_crypto = CryptoManager::new_with_algorithm(","highlight_start":62,"highlight_end":63},{"text":"        EncryptionAlgorithm::Aes128Cbc,","highlight_start":1,"highlight_end":40},{"text":"        \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),","highlight_start":1,"highlight_end":48},{"text":"        Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),","highlight_start":1,"highlight_end":54},{"text":"        None","highlight_start":1,"highlight_end":13},{"text":"    ).expect(\"Failed to create AES-128 manager\");","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":"(/* &str */, /* &str */, /* &str */, EncryptionAlgorithm::Aes128Cbc)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: arguments to this function are incorrect\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\end_to_end_encryption_integration_tests.rs:405:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m405\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    let mut aes128_crypto = CryptoManager::new_with_algorithm(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m406\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        EncryptionAlgorithm::Aes128Cbc,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m407\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #2 of type `std::string::String`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m408\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------------------------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|_________|__________________________________________|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthree arguments of type `&str`, `&str`, and `&str` are missing\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #3 of type `Option<std::string::String>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m409\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        None\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #4 of type `Option<_>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\Desktop\\rust_bypass1-5.28\\src\\core\\crypto.rs:31:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m31\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_with_algorithm(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: did you mean\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m405\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let mut aes128_crypto = CryptoManager::new_with_algorithm\u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m406\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         EncryptionAlgorithm::Aes128Cbc,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m407\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m408\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m409\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         None\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m410\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-     )\u001b[0m\u001b[0m.expect(\"Failed to create AES-128 manager\");\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m405\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let mut aes128_crypto = CryptoManager::new_with_algorithm\u001b[0m\u001b[0m\u001b[38;5;10m(/* &str */, /* &str */, /* &str */, EncryptionAlgorithm::Aes128Cbc)\u001b[0m\u001b[0m.expect(\"Failed to create AES-128 manager\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `encrypt_for_storage` found for struct `CryptoManager` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":15425,"byte_end":15444,"line_start":412,"line_end":412,"column_start":42,"column_end":61,"is_primary":true,"text":[{"text":"    let aes128_encrypted = aes128_crypto.encrypt_for_storage(&test_shellcode)","highlight_start":42,"highlight_end":61}],"label":"method not found in `CryptoManager`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `encrypt_for_storage` found for struct `CryptoManager` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\end_to_end_encryption_integration_tests.rs:412:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m412\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let aes128_encrypted = aes128_crypto.encrypt_for_storage(&test_shellcode)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `CryptoManager`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"arguments to this function are incorrect","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":17672,"byte_end":17710,"line_start":466,"line_end":466,"column_start":9,"column_end":47,"is_primary":false,"text":[{"text":"        \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),","highlight_start":9,"highlight_end":47}],"label":"unexpected argument #2 of type `std::string::String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":17720,"byte_end":17764,"line_start":467,"line_end":467,"column_start":9,"column_end":53,"is_primary":false,"text":[{"text":"        Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),","highlight_start":9,"highlight_end":53}],"label":"unexpected argument #3 of type `Option<std::string::String>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":17774,"byte_end":17778,"line_start":468,"line_end":468,"column_start":9,"column_end":13,"is_primary":false,"text":[{"text":"        None","highlight_start":9,"highlight_end":13}],"label":"unexpected argument #4 of type `Option<_>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":17632,"byte_end":17764,"line_start":465,"line_end":467,"column_start":9,"column_end":53,"is_primary":false,"text":[{"text":"        EncryptionAlgorithm::Aes128Cbc,","highlight_start":9,"highlight_end":40},{"text":"        \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),","highlight_start":1,"highlight_end":48},{"text":"        Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),","highlight_start":1,"highlight_end":53}],"label":"three arguments of type `&str`, `&str`, and `&str` are missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":17589,"byte_end":17622,"line_start":464,"line_end":464,"column_start":30,"column_end":63,"is_primary":true,"text":[{"text":"    let mut crypto_manager = CryptoManager::new_with_algorithm(","highlight_start":30,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\Desktop\\rust_bypass1-5.28\\src\\core\\crypto.rs","byte_start":1127,"byte_end":1145,"line_start":31,"line_end":31,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn new_with_algorithm(","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"did you mean","code":null,"level":"help","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":17622,"byte_end":17784,"line_start":464,"line_end":469,"column_start":63,"column_end":6,"is_primary":true,"text":[{"text":"    let mut crypto_manager = CryptoManager::new_with_algorithm(","highlight_start":63,"highlight_end":64},{"text":"        EncryptionAlgorithm::Aes128Cbc,","highlight_start":1,"highlight_end":40},{"text":"        \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),","highlight_start":1,"highlight_end":48},{"text":"        Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),","highlight_start":1,"highlight_end":54},{"text":"        None","highlight_start":1,"highlight_end":13},{"text":"    ).expect(\"Failed to create CryptoManager for security test\");","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":"(/* &str */, /* &str */, /* &str */, EncryptionAlgorithm::Aes128Cbc)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: arguments to this function are incorrect\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\end_to_end_encryption_integration_tests.rs:464:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m464\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    let mut crypto_manager = CryptoManager::new_with_algorithm(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m465\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        EncryptionAlgorithm::Aes128Cbc,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m466\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #2 of type `std::string::String`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m467\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------------------------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|_________|__________________________________________|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthree arguments of type `&str`, `&str`, and `&str` are missing\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #3 of type `Option<std::string::String>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m468\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        None\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #4 of type `Option<_>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\Desktop\\rust_bypass1-5.28\\src\\core\\crypto.rs:31:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m31\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_with_algorithm(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: did you mean\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m464\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let mut crypto_manager = CryptoManager::new_with_algorithm\u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m465\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         EncryptionAlgorithm::Aes128Cbc,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m466\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m467\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m468\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         None\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m469\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-     )\u001b[0m\u001b[0m.expect(\"Failed to create CryptoManager for security test\");\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m464\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let mut crypto_manager = CryptoManager::new_with_algorithm\u001b[0m\u001b[0m\u001b[38;5;10m(/* &str */, /* &str */, /* &str */, EncryptionAlgorithm::Aes128Cbc)\u001b[0m\u001b[0m.expect(\"Failed to create CryptoManager for security test\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `encrypt_for_storage` found for struct `CryptoManager` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":17891,"byte_end":17910,"line_start":471,"line_end":471,"column_start":46,"column_end":65,"is_primary":true,"text":[{"text":"    let encrypted_shellcode = crypto_manager.encrypt_for_storage(&test_shellcode)","highlight_start":46,"highlight_end":65}],"label":"method not found in `CryptoManager`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `encrypt_for_storage` found for struct `CryptoManager` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\end_to_end_encryption_integration_tests.rs:471:46\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m471\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let encrypted_shellcode = crypto_manager.encrypt_for_storage(&test_shellcode)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `CryptoManager`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"arguments to this function are incorrect","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":19898,"byte_end":19920,"line_start":525,"line_end":525,"column_start":9,"column_end":31,"is_primary":false,"text":[{"text":"        strong_key.to_string(),","highlight_start":9,"highlight_end":31}],"label":"unexpected argument #2 of type `std::string::String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":19930,"byte_end":19974,"line_start":526,"line_end":526,"column_start":9,"column_end":53,"is_primary":false,"text":[{"text":"        Some(\"U3Ryb25nSVYxMjM0NTY3ODkw\".to_string()),","highlight_start":9,"highlight_end":53}],"label":"unexpected argument #3 of type `Option<std::string::String>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":19984,"byte_end":19988,"line_start":527,"line_end":527,"column_start":9,"column_end":13,"is_primary":false,"text":[{"text":"        None","highlight_start":9,"highlight_end":13}],"label":"unexpected argument #4 of type `Option<_>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":19858,"byte_end":19974,"line_start":524,"line_end":526,"column_start":9,"column_end":53,"is_primary":false,"text":[{"text":"        EncryptionAlgorithm::Aes128Cbc,","highlight_start":9,"highlight_end":40},{"text":"        strong_key.to_string(),","highlight_start":1,"highlight_end":32},{"text":"        Some(\"U3Ryb25nSVYxMjM0NTY3ODkw\".to_string()),","highlight_start":1,"highlight_end":53}],"label":"three arguments of type `&str`, `&str`, and `&str` are missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":19815,"byte_end":19848,"line_start":523,"line_end":523,"column_start":30,"column_end":63,"is_primary":true,"text":[{"text":"    let mut crypto_manager = CryptoManager::new_with_algorithm(","highlight_start":30,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\Desktop\\rust_bypass1-5.28\\src\\core\\crypto.rs","byte_start":1127,"byte_end":1145,"line_start":31,"line_end":31,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn new_with_algorithm(","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"did you mean","code":null,"level":"help","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":19848,"byte_end":19994,"line_start":523,"line_end":528,"column_start":63,"column_end":6,"is_primary":true,"text":[{"text":"    let mut crypto_manager = CryptoManager::new_with_algorithm(","highlight_start":63,"highlight_end":64},{"text":"        EncryptionAlgorithm::Aes128Cbc,","highlight_start":1,"highlight_end":40},{"text":"        strong_key.to_string(),","highlight_start":1,"highlight_end":32},{"text":"        Some(\"U3Ryb25nSVYxMjM0NTY3ODkw\".to_string()),","highlight_start":1,"highlight_end":54},{"text":"        None","highlight_start":1,"highlight_end":13},{"text":"    ).expect(\"Failed to create CryptoManager with strong key\");","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":"(/* &str */, /* &str */, /* &str */, EncryptionAlgorithm::Aes128Cbc)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: arguments to this function are incorrect\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\end_to_end_encryption_integration_tests.rs:523:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m523\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    let mut crypto_manager = CryptoManager::new_with_algorithm(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m524\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        EncryptionAlgorithm::Aes128Cbc,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m525\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        strong_key.to_string(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #2 of type `std::string::String`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m526\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Some(\"U3Ryb25nSVYxMjM0NTY3ODkw\".to_string()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------------------------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|_________|__________________________________________|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthree arguments of type `&str`, `&str`, and `&str` are missing\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #3 of type `Option<std::string::String>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m527\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        None\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #4 of type `Option<_>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\Desktop\\rust_bypass1-5.28\\src\\core\\crypto.rs:31:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m31\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_with_algorithm(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: did you mean\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m523\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let mut crypto_manager = CryptoManager::new_with_algorithm\u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m524\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         EncryptionAlgorithm::Aes128Cbc,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m525\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         strong_key.to_string(),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m526\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         Some(\"U3Ryb25nSVYxMjM0NTY3ODkw\".to_string()),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m527\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         None\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m528\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-     )\u001b[0m\u001b[0m.expect(\"Failed to create CryptoManager with strong key\");\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m523\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let mut crypto_manager = CryptoManager::new_with_algorithm\u001b[0m\u001b[0m\u001b[38;5;10m(/* &str */, /* &str */, /* &str */, EncryptionAlgorithm::Aes128Cbc)\u001b[0m\u001b[0m.expect(\"Failed to create CryptoManager with strong key\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `encrypt_for_storage` found for struct `CryptoManager` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":20099,"byte_end":20118,"line_start":530,"line_end":530,"column_start":46,"column_end":65,"is_primary":true,"text":[{"text":"    let encrypted_shellcode = crypto_manager.encrypt_for_storage(&test_shellcode)","highlight_start":46,"highlight_end":65}],"label":"method not found in `CryptoManager`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `encrypt_for_storage` found for struct `CryptoManager` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\end_to_end_encryption_integration_tests.rs:530:46\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m530\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let encrypted_shellcode = crypto_manager.encrypt_for_storage(&test_shellcode)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `CryptoManager`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"arguments to this function are incorrect","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":22282,"byte_end":22305,"line_start":587,"line_end":587,"column_start":9,"column_end":32,"is_primary":false,"text":[{"text":"        invalid_key.to_string(),","highlight_start":9,"highlight_end":32}],"label":"unexpected argument #2 of type `std::string::String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":22315,"byte_end":22359,"line_start":588,"line_end":588,"column_start":9,"column_end":53,"is_primary":false,"text":[{"text":"        Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),","highlight_start":9,"highlight_end":53}],"label":"unexpected argument #3 of type `Option<std::string::String>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":22369,"byte_end":22373,"line_start":589,"line_end":589,"column_start":9,"column_end":13,"is_primary":false,"text":[{"text":"        None","highlight_start":9,"highlight_end":13}],"label":"unexpected argument #4 of type `Option<_>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":22242,"byte_end":22359,"line_start":586,"line_end":588,"column_start":9,"column_end":53,"is_primary":false,"text":[{"text":"        EncryptionAlgorithm::Aes128Cbc,","highlight_start":9,"highlight_end":40},{"text":"        invalid_key.to_string(),","highlight_start":1,"highlight_end":33},{"text":"        Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),","highlight_start":1,"highlight_end":53}],"label":"three arguments of type `&str`, `&str`, and `&str` are missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":22199,"byte_end":22232,"line_start":585,"line_end":585,"column_start":37,"column_end":70,"is_primary":true,"text":[{"text":"    let mut crypto_manager_result = CryptoManager::new_with_algorithm(","highlight_start":37,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\Desktop\\rust_bypass1-5.28\\src\\core\\crypto.rs","byte_start":1127,"byte_end":1145,"line_start":31,"line_end":31,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn new_with_algorithm(","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"did you mean","code":null,"level":"help","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":22232,"byte_end":22379,"line_start":585,"line_end":590,"column_start":70,"column_end":6,"is_primary":true,"text":[{"text":"    let mut crypto_manager_result = CryptoManager::new_with_algorithm(","highlight_start":70,"highlight_end":71},{"text":"        EncryptionAlgorithm::Aes128Cbc,","highlight_start":1,"highlight_end":40},{"text":"        invalid_key.to_string(),","highlight_start":1,"highlight_end":33},{"text":"        Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),","highlight_start":1,"highlight_end":54},{"text":"        None","highlight_start":1,"highlight_end":13},{"text":"    );","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":"(/* &str */, /* &str */, /* &str */, EncryptionAlgorithm::Aes128Cbc)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: arguments to this function are incorrect\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\end_to_end_encryption_integration_tests.rs:585:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m585\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    let mut crypto_manager_result = CryptoManager::new_with_algorithm(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m586\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        EncryptionAlgorithm::Aes128Cbc,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m587\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        invalid_key.to_string(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #2 of type `std::string::String`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m588\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------------------------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|_________|__________________________________________|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthree arguments of type `&str`, `&str`, and `&str` are missing\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #3 of type `Option<std::string::String>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m589\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        None\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #4 of type `Option<_>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\Desktop\\rust_bypass1-5.28\\src\\core\\crypto.rs:31:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m31\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_with_algorithm(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: did you mean\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m585\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let mut crypto_manager_result = CryptoManager::new_with_algorithm\u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m586\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         EncryptionAlgorithm::Aes128Cbc,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m587\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         invalid_key.to_string(),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m588\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         Some(\"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string()),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m589\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         None\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m590\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-     )\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m585\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let mut crypto_manager_result = CryptoManager::new_with_algorithm\u001b[0m\u001b[0m\u001b[38;5;10m(/* &str */, /* &str */, /* &str */, EncryptionAlgorithm::Aes128Cbc)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"arguments to this function are incorrect","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":22737,"byte_end":22775,"line_start":599,"line_end":599,"column_start":9,"column_end":47,"is_primary":false,"text":[{"text":"        \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),","highlight_start":9,"highlight_end":47}],"label":"unexpected argument #2 of type `std::string::String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":22785,"byte_end":22813,"line_start":600,"line_end":600,"column_start":9,"column_end":37,"is_primary":false,"text":[{"text":"        Some(\"c2hvcnQ=\".to_string()), // 太短的IV","highlight_start":9,"highlight_end":37}],"label":"unexpected argument #3 of type `Option<std::string::String>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":22838,"byte_end":22842,"line_start":601,"line_end":601,"column_start":9,"column_end":13,"is_primary":false,"text":[{"text":"        None","highlight_start":9,"highlight_end":13}],"label":"unexpected argument #4 of type `Option<_>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":22697,"byte_end":22813,"line_start":598,"line_end":600,"column_start":9,"column_end":37,"is_primary":false,"text":[{"text":"        EncryptionAlgorithm::Aes128Cbc,","highlight_start":9,"highlight_end":40},{"text":"        \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),","highlight_start":1,"highlight_end":48},{"text":"        Some(\"c2hvcnQ=\".to_string()), // 太短的IV","highlight_start":1,"highlight_end":37}],"label":"three arguments of type `&str`, `&str`, and `&str` are missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":22654,"byte_end":22687,"line_start":597,"line_end":597,"column_start":33,"column_end":66,"is_primary":true,"text":[{"text":"    let crypto_manager_result = CryptoManager::new_with_algorithm(","highlight_start":33,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\Desktop\\rust_bypass1-5.28\\src\\core\\crypto.rs","byte_start":1127,"byte_end":1145,"line_start":31,"line_end":31,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn new_with_algorithm(","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"did you mean","code":null,"level":"help","spans":[{"file_name":"tests\\end_to_end_encryption_integration_tests.rs","byte_start":22687,"byte_end":22848,"line_start":597,"line_end":602,"column_start":66,"column_end":6,"is_primary":true,"text":[{"text":"    let crypto_manager_result = CryptoManager::new_with_algorithm(","highlight_start":66,"highlight_end":67},{"text":"        EncryptionAlgorithm::Aes128Cbc,","highlight_start":1,"highlight_end":40},{"text":"        \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),","highlight_start":1,"highlight_end":48},{"text":"        Some(\"c2hvcnQ=\".to_string()), // 太短的IV","highlight_start":1,"highlight_end":47},{"text":"        None","highlight_start":1,"highlight_end":13},{"text":"    );","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":"(/* &str */, /* &str */, /* &str */, EncryptionAlgorithm::Aes128Cbc)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: arguments to this function are incorrect\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\end_to_end_encryption_integration_tests.rs:597:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m597\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    let crypto_manager_result = CryptoManager::new_with_algorithm(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m598\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        EncryptionAlgorithm::Aes128Cbc,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m599\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #2 of type `std::string::String`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m600\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Some(\"c2hvcnQ=\".to_string()), // 太短的IV\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|_________|__________________________|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthree arguments of type `&str`, `&str`, and `&str` are missing\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #3 of type `Option<std::string::String>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m601\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        None\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14munexpected argument #4 of type `Option<_>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\Desktop\\rust_bypass1-5.28\\src\\core\\crypto.rs:31:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m31\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new_with_algorithm(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: did you mean\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m597\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let crypto_manager_result = CryptoManager::new_with_algorithm\u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m598\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         EncryptionAlgorithm::Aes128Cbc,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m599\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         \"MTIzNDU2Nzg5MGFiY2RlZg==\".to_string(),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m600\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         Some(\"c2hvcnQ=\".to_string()), // 太短的IV\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m601\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-         None\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m602\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-     )\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m597\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let crypto_manager_result = CryptoManager::new_with_algorithm\u001b[0m\u001b[0m\u001b[38;5;10m(/* &str */, /* &str */, /* &str */, EncryptionAlgorithm::Aes128Cbc)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 12 previous errors; 1 warning emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 12 previous errors; 1 warning emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0308, E0599.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0308, E0599.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0308`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0308`.\u001b[0m\n"}
