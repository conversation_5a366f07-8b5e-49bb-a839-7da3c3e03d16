{"rustc": 8024708092284749966, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2241668132362809309, "path": 9421104762069299659, "deps": [[555019317135488525, "regex_automata", false, 11843269137050584685], [2779309023524819297, "aho_corasick", false, 9087892282893601323], [3129130049864710036, "memchr", false, 5852662608161164150], [9408802513701742484, "regex_syntax", false, 16405491807662845391]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-3efa25cdcdbe4d82\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}