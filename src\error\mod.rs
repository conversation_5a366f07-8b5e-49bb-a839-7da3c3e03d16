use std::error::Error;
use std::fmt;

/// 项目中所有可能的错误类型
#[derive(Debug)] // Removed Clone and PartialEq
pub enum BypassError {
    /// 内存分配错误
    MemoryAllocationError(i32),
    /// 内存写入错误
    MemoryWriteError(i32),
    /// 内存保护属性修改错误
    MemoryProtectionError(i32),
    /// 线程创建错误
    ThreadCreationError(i32),
    /// APC队列操作错误
    ApcQueueError(i32),
    /// 解密错误
    DecryptionError(String),
    /// 加密错误
    EncryptionError(String),
    /// 检测到沙箱环境
    SandboxDetected,
    /// 文件操作错误
    FileError(std::io::Error),
    /// 日志初始化错误
    LoggerError(String),
    /// 执行错误
    ExecutionError(String),
    /// 内部逻辑错误
    InternalError(String),
    /// 资源清理错误
    ResourceCleanupError(i32),
    /// 初始化错误
    InitializationError(String),
    /// 无效参数错误
    InvalidParameter(String),
    /// Fiber 操作错误
    FiberError(String),
    /// 其他错误
    Other(String),
    /// Native API 调用失败
    NativeApiFailure {
        api_name: String,
        error_code: Option<i32>,
    },
    /// SyscallManager 内部错误
    SyscallManagerError(String),
    /// 系统调用返回的 NTSTATUS 非成功错误
    SyscallNtStatusError {
        function_name: String,
        status_code: i32,
    },
    /// AMSI 绕过特定错误
    AmsiError(String),
    /// ETW 绕过特定错误
    EtwError(String),
    /// 未找到模块错误
    ModuleNotFound(String),
    /// 未找到函数错误
    FunctionNotFound(String),
    /// 未找到进程错误
    ProcessNotFound(String),
    /// 句柄操作错误
    HandleError(String),
    /// 进程创建错误
    ProcessCreationError(String),
    ResourceNotFound(String),            // Added
    ResourceInitializationError(String), // Added for HeapProtectAllocator
    NotImplemented(String),              // Added for placeholder implementations
    PeParsingError(String),              // Added for PE parsing errors
}

impl fmt::Display for BypassError {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            BypassError::MemoryAllocationError(code) => write!(f, "内存分配失败，错误码: {}", code),
            BypassError::MemoryWriteError(code) => write!(f, "内存写入失败，错误码: {}", code),
            BypassError::MemoryProtectionError(code) => {
                write!(f, "内存保护属性修改失败，错误码: {}", code)
            }
            BypassError::ThreadCreationError(code) => write!(f, "线程创建失败，错误码: {}", code),
            BypassError::ApcQueueError(code) => write!(f, "APC队列操作失败，错误码: {}", code),
            BypassError::DecryptionError(msg) => write!(f, "解密失败: {}", msg),
            BypassError::EncryptionError(msg) => write!(f, "加密失败: {}", msg),
            BypassError::SandboxDetected => write!(f, "检测到沙箱环境"),
            BypassError::FileError(e) => write!(f, "文件操作错误: {}", e),
            BypassError::LoggerError(msg) => write!(f, "日志初始化错误: {}", msg),
            BypassError::ExecutionError(msg) => write!(f, "执行错误: {}", msg),
            BypassError::InternalError(msg) => write!(f, "内部错误: {}", msg),
            BypassError::ResourceCleanupError(code) => write!(f, "资源清理失败，错误码: {}", code),
            BypassError::InitializationError(msg) => write!(f, "初始化失败: {}", msg),
            BypassError::InvalidParameter(msg) => write!(f, "无效参数: {}", msg),
            BypassError::FiberError(msg) => write!(f, "Fiber 操作错误: {}", msg),
            BypassError::Other(msg) => write!(f, "其他错误: {}", msg),
            BypassError::NativeApiFailure {
                api_name,
                error_code,
            } => {
                if let Some(code) = error_code {
                    write!(f, "Native API '{}' 调用失败，错误码: {}", api_name, code)
                } else {
                    write!(f, "Native API '{}' 调用失败，无特定错误码", api_name)
                }
            }
            BypassError::SyscallManagerError(msg) => write!(f, "SyscallManager 错误: {}", msg),
            BypassError::SyscallNtStatusError {
                function_name,
                status_code,
            } => write!(
                f,
                "系统调用 '{}' 失败，NTSTATUS: {:#X}",
                function_name, status_code
            ),
            BypassError::AmsiError(msg) => write!(f, "AMSI Bypass 错误: {}", msg),
            BypassError::EtwError(msg) => write!(f, "ETW Bypass 错误: {}", msg),
            BypassError::ModuleNotFound(name) => write!(f, "模块 '{}' 未找到", name),
            BypassError::FunctionNotFound(name) => write!(f, "函数 '{}' 未找到", name),
            BypassError::ProcessNotFound(name) => write!(f, "进程 '{}' 未找到", name),
            BypassError::HandleError(msg) => write!(f, "句柄操作错误: {}", msg),
            BypassError::ProcessCreationError(msg) => write!(f, "进程创建错误: {}", msg),
            BypassError::ResourceNotFound(name) =>
            // Added
            {
                write!(f, "资源 '{}' 未找到", name)
            }
            BypassError::ResourceInitializationError(msg) =>
            // Added for HeapProtectAllocator
            {
                write!(f, "资源初始化失败: {}", msg)
            }
            BypassError::NotImplemented(feature) =>
            // Added for placeholder implementations
            {
                write!(f, "功能尚未实现: {}", feature)
            }
            BypassError::PeParsingError(msg) => write!(f, "PE解析错误: {}", msg),
        }
    }
}

impl Error for BypassError {}

// 从 std::io::Error 转换
impl From<std::io::Error> for BypassError {
    fn from(error: std::io::Error) -> Self {
        BypassError::FileError(error)
    }
}

// 从 log::SetLoggerError 转换
impl From<log::SetLoggerError> for BypassError {
    fn from(error: log::SetLoggerError) -> Self {
        BypassError::LoggerError(error.to_string())
    }
}

/// 项目中使用的结果类型
pub type BypassResult<T> = Result<T, BypassError>;
