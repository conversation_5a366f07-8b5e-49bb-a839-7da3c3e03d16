{"$message_type":"diagnostic","message":"missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`","code":{"code":"E0063","explanation":"A struct's or struct-like enum variant's field was not provided.\n\nErroneous code example:\n\n```compile_fail,E0063\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0 }; // error: missing field: `y`\n}\n```\n\nEach field should be specified exactly once. Example:\n\n```\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0, y: 0 }; // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"tests\\evasion_manager_integration_tests.rs","byte_start":923,"byte_end":936,"line_start":23,"line_end":23,"column_start":24,"column_end":37,"is_primary":true,"text":[{"text":"    let empty_config = EvasionConfig {","highlight_start":24,"highlight_end":37}],"label":"missing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0063]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\evasion_manager_integration_tests.rs:23:24\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let empty_config = EvasionConfig {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmissing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`","code":{"code":"E0063","explanation":"A struct's or struct-like enum variant's field was not provided.\n\nErroneous code example:\n\n```compile_fail,E0063\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0 }; // error: missing field: `y`\n}\n```\n\nEach field should be specified exactly once. Example:\n\n```\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0, y: 0 }; // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"tests\\evasion_manager_integration_tests.rs","byte_start":1452,"byte_end":1465,"line_start":42,"line_end":42,"column_start":24,"column_end":37,"is_primary":true,"text":[{"text":"    let empty_config = EvasionConfig {","highlight_start":24,"highlight_end":37}],"label":"missing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0063]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\evasion_manager_integration_tests.rs:42:24\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let empty_config = EvasionConfig {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmissing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`","code":{"code":"E0063","explanation":"A struct's or struct-like enum variant's field was not provided.\n\nErroneous code example:\n\n```compile_fail,E0063\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0 }; // error: missing field: `y`\n}\n```\n\nEach field should be specified exactly once. Example:\n\n```\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0, y: 0 }; // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"tests\\evasion_manager_integration_tests.rs","byte_start":1936,"byte_end":1949,"line_start":60,"line_end":60,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"    let config = EvasionConfig {","highlight_start":18,"highlight_end":31}],"label":"missing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0063]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\evasion_manager_integration_tests.rs:60:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m60\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let config = EvasionConfig {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmissing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`","code":{"code":"E0063","explanation":"A struct's or struct-like enum variant's field was not provided.\n\nErroneous code example:\n\n```compile_fail,E0063\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0 }; // error: missing field: `y`\n}\n```\n\nEach field should be specified exactly once. Example:\n\n```\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0, y: 0 }; // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"tests\\evasion_manager_integration_tests.rs","byte_start":3024,"byte_end":3037,"line_start":97,"line_end":97,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"    let config = EvasionConfig {","highlight_start":18,"highlight_end":31}],"label":"missing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0063]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\evasion_manager_integration_tests.rs:97:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m97\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let config = EvasionConfig {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmissing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`","code":{"code":"E0063","explanation":"A struct's or struct-like enum variant's field was not provided.\n\nErroneous code example:\n\n```compile_fail,E0063\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0 }; // error: missing field: `y`\n}\n```\n\nEach field should be specified exactly once. Example:\n\n```\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0, y: 0 }; // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"tests\\evasion_manager_integration_tests.rs","byte_start":3976,"byte_end":3989,"line_start":130,"line_end":130,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"    let config = EvasionConfig {","highlight_start":18,"highlight_end":31}],"label":"missing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0063]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\evasion_manager_integration_tests.rs:130:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m130\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let config = EvasionConfig {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmissing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`","code":{"code":"E0063","explanation":"A struct's or struct-like enum variant's field was not provided.\n\nErroneous code example:\n\n```compile_fail,E0063\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0 }; // error: missing field: `y`\n}\n```\n\nEach field should be specified exactly once. Example:\n\n```\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0, y: 0 }; // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"tests\\evasion_manager_integration_tests.rs","byte_start":4931,"byte_end":4944,"line_start":163,"line_end":163,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"    let config = EvasionConfig {","highlight_start":18,"highlight_end":31}],"label":"missing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0063]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\evasion_manager_integration_tests.rs:163:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m163\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let config = EvasionConfig {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmissing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`","code":{"code":"E0063","explanation":"A struct's or struct-like enum variant's field was not provided.\n\nErroneous code example:\n\n```compile_fail,E0063\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0 }; // error: missing field: `y`\n}\n```\n\nEach field should be specified exactly once. Example:\n\n```\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0, y: 0 }; // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"tests\\evasion_manager_integration_tests.rs","byte_start":6181,"byte_end":6194,"line_start":203,"line_end":203,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"    let config = EvasionConfig {","highlight_start":18,"highlight_end":31}],"label":"missing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0063]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\evasion_manager_integration_tests.rs:203:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m203\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let config = EvasionConfig {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmissing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`","code":{"code":"E0063","explanation":"A struct's or struct-like enum variant's field was not provided.\n\nErroneous code example:\n\n```compile_fail,E0063\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0 }; // error: missing field: `y`\n}\n```\n\nEach field should be specified exactly once. Example:\n\n```\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0, y: 0 }; // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"tests\\evasion_manager_integration_tests.rs","byte_start":7439,"byte_end":7452,"line_start":245,"line_end":245,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"    let config = EvasionConfig {","highlight_start":18,"highlight_end":31}],"label":"missing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0063]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\evasion_manager_integration_tests.rs:245:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m245\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let config = EvasionConfig {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmissing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`","code":{"code":"E0063","explanation":"A struct's or struct-like enum variant's field was not provided.\n\nErroneous code example:\n\n```compile_fail,E0063\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0 }; // error: missing field: `y`\n}\n```\n\nEach field should be specified exactly once. Example:\n\n```\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0, y: 0 }; // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"tests\\evasion_manager_integration_tests.rs","byte_start":8200,"byte_end":8213,"line_start":271,"line_end":271,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"    let config = EvasionConfig {","highlight_start":18,"highlight_end":31}],"label":"missing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0063]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\evasion_manager_integration_tests.rs:271:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m271\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let config = EvasionConfig {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmissing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`","code":{"code":"E0063","explanation":"A struct's or struct-like enum variant's field was not provided.\n\nErroneous code example:\n\n```compile_fail,E0063\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0 }; // error: missing field: `y`\n}\n```\n\nEach field should be specified exactly once. Example:\n\n```\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0, y: 0 }; // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"tests\\evasion_manager_integration_tests.rs","byte_start":9152,"byte_end":9165,"line_start":305,"line_end":305,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"    let config = EvasionConfig {","highlight_start":18,"highlight_end":31}],"label":"missing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0063]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\evasion_manager_integration_tests.rs:305:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let config = EvasionConfig {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmissing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`","code":{"code":"E0063","explanation":"A struct's or struct-like enum variant's field was not provided.\n\nErroneous code example:\n\n```compile_fail,E0063\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0 }; // error: missing field: `y`\n}\n```\n\nEach field should be specified exactly once. Example:\n\n```\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0, y: 0 }; // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"tests\\evasion_manager_integration_tests.rs","byte_start":10030,"byte_end":10043,"line_start":335,"line_end":335,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"    let config = EvasionConfig {","highlight_start":18,"highlight_end":31}],"label":"missing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0063]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing fields `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields in initializer of `EvasionConfig`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mtests\\evasion_manager_integration_tests.rs:335:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m335\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let config = EvasionConfig {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmissing `anti_hook`, `cmdline_spoof`, `etw` and 3 other fields\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 11 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 11 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0063`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0063`.\u001b[0m\n"}
