use serde::{Deserialize, Serialize};

/// 定义不同的 Shellcode 执行策略类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ExecutionStrategyType {
    DirectThread,
    ApcInjection,
    Fiber,
    SystemCallback, // 占位
    HardwareBreakpoint, // 占位
    ThreadContextHijack, // 占位
}

impl Default for ExecutionStrategyType {
    fn default() -> Self {
        ExecutionStrategyType::DirectThread
    }
}

/// DirectThread 策略配置
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct DirectThreadConfig {
    // 未来可以添加特定于直接线程的配置，例如线程创建标志
    pub example_param: Option<String>, // 示例参数
}

/// APC 注入的目标线程类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ApcTargetType {
    NewThread,
    ExistingThread,
}

impl Default for ApcTargetType {
    fn default() -> Self {
        ApcTargetType::NewThread
    }
}

/// APC 注入策略相关配置
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ApcInjectionConfig {
    pub target_type: ApcTargetType,
    // 可以添加更多特定于 APC 注入的配置
}

/// Fiber 策略配置
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct FiberConfig {
    /// Fiber 的栈大小 (字节)。如果为 None 或 0，则使用默认栈大小。
    pub stack_size: Option<usize>,
    /// 传递给Fiber的数据
    pub fiber_data: Option<Vec<u8>>,
    /// 执行超时（毫秒）
    pub timeout_ms: Option<u32>,
    /// 是否启用异常处理
    pub enable_exception_handling: bool,
}

/// Timer回调类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum TimerType {
    /// 使用SetTimer (窗口定时器)
    SetTimer,
    /// 使用CreateTimerQueueTimer (线程池定时器)
    QueueTimer,
    /// 使用CreateWaitableTimer (可等待定时器)
    WaitableTimer,
}

impl Default for TimerType {
    fn default() -> Self {
        TimerType::QueueTimer
    }
}

/// APC回调类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ApcCallbackType {
    /// 用户模式APC
    UserMode,
    /// 内核模式APC (需要特殊权限)
    KernelMode,
}

impl Default for ApcCallbackType {
    fn default() -> Self {
        ApcCallbackType::UserMode
    }
}

/// 硬件断点类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum BreakpointType {
    /// 执行断点
    Execute,
    /// 读取断点
    Read,
    /// 写入断点
    Write,
    /// 读写断点
    ReadWrite,
}

impl Default for BreakpointType {
    fn default() -> Self {
        BreakpointType::Execute
    }
}

/// 硬件断点条件
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum BreakpointCondition {
    /// 无条件触发
    Always,
    /// 特定值匹配
    ValueMatch(u64),
    /// 范围匹配
    RangeMatch(u64, u64),
}

impl Default for BreakpointCondition {
    fn default() -> Self {
        BreakpointCondition::Always
    }
}

/// Timer回调配置
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct TimerConfig {
    /// 定时器类型
    pub timer_type: TimerType,
    /// 延迟时间（毫秒）
    pub delay_ms: u32,
    /// 周期性执行间隔（毫秒），None表示只执行一次
    pub period_ms: Option<u32>,
    /// 是否使用高精度定时器
    pub high_precision: bool,
    /// 执行超时（毫秒）
    pub timeout_ms: Option<u32>,
}

impl Default for TimerConfig {
    fn default() -> Self {
        Self {
            timer_type: TimerType::default(),
            delay_ms: 1000, // 1秒延迟
            period_ms: None, // 只执行一次
            high_precision: false,
            timeout_ms: Some(30000), // 30秒超时
        }
    }
}

/// APC回调配置
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct ApcConfig {
    /// APC类型
    pub apc_type: ApcCallbackType,
    /// 目标线程ID（可选）
    pub target_thread_id: Option<u32>,
    /// 是否创建新线程
    pub create_new_thread: bool,
    /// 执行超时（毫秒）
    pub timeout_ms: Option<u32>,
}

impl Default for ApcConfig {
    fn default() -> Self {
        Self {
            apc_type: ApcCallbackType::default(),
            target_thread_id: None,
            create_new_thread: true,
            timeout_ms: Some(30000), // 30秒超时
        }
    }
}

/// 硬件断点配置
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct HardwareBreakpointConfig {
    /// 断点类型
    pub breakpoint_type: BreakpointType,
    /// 目标地址（None表示自动选择）
    pub target_address: Option<usize>,
    /// 断点条件
    pub condition: BreakpointCondition,
    /// 执行超时（毫秒）
    pub timeout_ms: Option<u32>,
}

impl Default for HardwareBreakpointConfig {
    fn default() -> Self {
        Self {
            breakpoint_type: BreakpointType::default(),
            target_address: None,
            condition: BreakpointCondition::default(),
            timeout_ms: Some(30000), // 30秒超时
        }
    }
}

/// 系统回调策略类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum SystemCallbackType {
    /// Timer回调
    Timer(TimerConfig),
    /// APC回调
    Apc(ApcConfig),
    /// 硬件断点回调
    HardwareBreakpoint(HardwareBreakpointConfig),
}

impl Default for SystemCallbackType {
    fn default() -> Self {
        SystemCallbackType::Timer(TimerConfig::default())
    }
}

/// SystemCallback 策略配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemCallbackConfig {
    /// 回调类型
    pub callback_type: SystemCallbackType,
    /// 是否启用异常处理
    pub enable_exception_handling: bool,
    /// 是否启用详细日志
    pub enable_verbose_logging: bool,
}

impl Default for SystemCallbackConfig {
    fn default() -> Self {
        Self {
            callback_type: SystemCallbackType::default(),
            enable_exception_handling: true,
            enable_verbose_logging: false,
        }
    }
}



/// ThreadContextHijack 策略配置 (占位)
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ThreadContextHijackConfig {
    // 未来配置
}

/// 具体的执行策略配置枚举
///
/// 这个枚举封装了每种执行策略可能需要的特定配置。
/// `ExecutionManager` 将根据这个配置来选择和执行相应的 Shellcode 注入和运行方法。
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "strategy_type")] // 有助于序列化/反序列化
pub enum ExecutionStrategyConfig {
    DirectThread(DirectThreadConfig),
    ApcInjection(ApcInjectionConfig),
    Fiber(FiberConfig),
    SystemCallback(SystemCallbackConfig),
    HardwareBreakpoint(HardwareBreakpointConfig),
    ThreadContextHijack(ThreadContextHijackConfig),
}

impl Default for ExecutionStrategyConfig {
    fn default() -> Self {
        ExecutionStrategyConfig::DirectThread(DirectThreadConfig::default())
    }
}

// 保留旧的 ExecutionConfig 结构，可能用于全局配置或向后兼容，
// 但 ExecutionManager 将主要使用 ExecutionStrategyConfig
// 或者，如果 ExecutionConfig 旨在包含所有可能的策略配置，
// 那么 ExecutionManager 可以引用它，并由 ExecutionStrategyType 指导。
// 当前任务要求 ExecutionManager 直接使用 ExecutionStrategyConfig 及其子配置。

/// 全局执行配置，可能包含所有策略的使能开关和默认配置
/// 这个结构体可以被 Orchestrator 用来加载总的执行相关配置
/// 然后根据需要创建具体的 ExecutionStrategyConfig 传递给 ExecutionManager
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalExecutionConfig {
    pub default_strategy: ExecutionStrategyType,
    pub direct_thread_config: DirectThreadConfig,
    pub apc_injection_config: ApcInjectionConfig,
    pub fiber_config: FiberConfig,
    pub system_callback_config: SystemCallbackConfig,
    pub hardware_breakpoint_config: HardwareBreakpointConfig,
    pub thread_context_hijack_config: ThreadContextHijackConfig,
    // 例如，可以添加一个字段来决定当请求的策略未实现时是否回退到默认策略
    pub fallback_to_default_if_unsupported: bool,
}

impl Default for GlobalExecutionConfig {
    fn default() -> Self {
        GlobalExecutionConfig {
            default_strategy: ExecutionStrategyType::default(),
            direct_thread_config: DirectThreadConfig::default(),
            apc_injection_config: ApcInjectionConfig::default(),
            fiber_config: FiberConfig::default(),
            system_callback_config: SystemCallbackConfig::default(),
            hardware_breakpoint_config: HardwareBreakpointConfig::default(),
            thread_context_hijack_config: ThreadContextHijackConfig::default(),
            fallback_to_default_if_unsupported: true,
        }
    }
}
