C:\Users\<USER>\Desktop\rust_bypass1-5.28\target\release\librust_bypassAV.rlib: C:\Users\<USER>\Desktop\rust_bypass1-5.28\beacon.bin C:\Users\<USER>\Desktop\rust_bypass1-5.28\build.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\config.json C:\Users\<USER>\Desktop\rust_bypass1-5.28\shellcode_encrypted.txt C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\config\defense_config.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\config\execution_config.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\config\loader.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\config\memory_config.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\config\mod.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\config\sandbox_config.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\constants.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\core\crypto.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\core\embedded.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\core\encrypted_memory.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\core\execution.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\core\memory.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\core\mod.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\core\shellcode_loader.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\core\syscalls.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\defense\amsi.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\defense\cmdline.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\defense\etw.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\defense\evasion_manager.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\defense\identity.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\defense\mod.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\defense\ppid.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\defense\sandbox.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\error\mod.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\lib.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\orchestrator.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\utils\code_cave_scanner.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\utils\logger.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\utils\mod.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\utils\pe_parser.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\utils\random.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\utils\winapi.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\src\utils\winapi_utils.rs C:\Users\<USER>\Desktop\rust_bypass1-5.28\target\release\build\rust-bypassAV-5fcd884c8dd68e37\out\embedded_config.json C:\Users\<USER>\Desktop\rust_bypass1-5.28\target\release\build\rust-bypassAV-5fcd884c8dd68e37\out\embedded_shellcode.rs
