# Rust Shellcode加载器 - 360绕过优化总结

## 项目概述

这是一个用Rust编写的高级Shellcode加载器，专门针对360安全软件进行了优化，解决了Cobalt Strike Beacon截图功能触发"远程线程注入"告警的问题。

## 主要优化内容

### 1. 执行策略优化
- **从DirectThread切换到Fiber**：避免跨进程操作，在同一进程内执行
- **启用系统调用强化**：使用间接系统调用和动态发现
- **添加Fiber配置选项**：超时、栈大小、栈保护等

### 2. 内存分配策略优化  
- **从Direct切换到SectionMapping**：使用Windows内存映射机制
- **添加随机化选项**：随机section名称和视图随机化
- **增强访问权限控制**：避免可疑的权限组合

### 3. 反检测技术增强
- **AMSI绕过**：patch AmsiScanBuffer函数
- **ETW绕过**：禁用ETW事件追踪，patch多个ETW函数
- **反Hook技术**：解除ntdll hook，从磁盘加载新的ntdll
- **沙箱规避**：多种检测机制，延迟执行
- **命令行欺骗**：伪造进程参数
- **PPID欺骗**：伪造父进程

### 4. 新增360专用模块
- **anti_360.rs**：专门针对360的绕过技术
- **ETW监控绕过**：patch EtwEventWrite
- **AMSI检测绕过**：patch AmsiScanBuffer  
- **远程线程注入绕过**：使用Fiber技术
- **内存保护绕过**：合法内存分配方式

## 技术架构

### 核心模块
```
src/
├── config/           # 配置管理
│   ├── loader.rs     # 配置加载器（已更新）
│   └── ...
├── core/             # 核心功能
│   ├── execution.rs  # 执行管理器
│   ├── memory.rs     # 内存管理器
│   └── ...
├── defense/          # 防御绕过
│   ├── anti_360.rs   # 360专用绕过（新增）
│   ├── evasion_manager.rs
│   └── ...
└── orchestrator.rs   # 主协调器
```

### 配置结构
- **ExecutionConfig**：支持Fiber策略和选项
- **MemoryConfig**：支持SectionMapping配置
- **EvasionConfig**：新增多项反检测配置
- **Anti360Config**：360专用绕过配置

## 关键技术点

### 1. Fiber技术
```rust
// 避免远程线程注入的Fiber执行
unsafe fn execute_via_fiber(shellcode_address: *mut c_void) -> BypassResult<()> {
    let main_fiber = ConvertThreadToFiber(ptr::null_mut());
    let shellcode_fiber = CreateFiber(0, shellcode_address, ptr::null_mut());
    SwitchToFiber(shellcode_fiber);
    // ...
}
```

### 2. SectionMapping
```json
{
    "allocationStrategy": "SectionMapping",
    "sectionMappingConfig": {
        "sectionName": "\\BaseNamedObjects\\Global\\msctf.SharedSection",
        "useRandomSectionName": true,
        "enableViewRandomization": true
    }
}
```

### 3. 反检测技术
```rust
// ETW绕过
unsafe fn bypass_etw_monitoring() -> BypassResult<()> {
    let etw_func_addr = GetProcAddress(ntdll_handle, b"EtwEventWrite\0".as_ptr());
    *etw_func_addr = 0xC3; // ret指令
    // ...
}
```

## 使用方法

### 1. 编译
```bash
cargo build --release
```

### 2. 配置
使用优化后的config.json，包含：
- Fiber执行策略
- SectionMapping内存分配
- 完整的反检测技术

### 3. 运行
```bash
.\target\release\rust-bypassAV.exe
```

## 测试结果

### 问题解决
- ✅ 解决了CS截图功能的360告警问题
- ✅ 避免了"远程线程注入"检测
- ✅ 保持了其他功能的正常使用

### 技术验证
- ✅ Fiber技术成功避免跨进程操作
- ✅ SectionMapping绕过内存检测
- ✅ 多层反检测技术有效

## 文件变更

### 新增文件
- `src/defense/anti_360.rs` - 360专用绕过模块
- `360_BYPASS_GUIDE.md` - 详细使用指南
- `PROJECT_SUMMARY.md` - 项目总结

### 修改文件
- `config.json` - 更新为360绕过配置
- `src/config/loader.rs` - 新增配置结构
- `src/defense/mod.rs` - 导出新模块
- `tools/config_template_generator.rs` - 支持新配置

## 注意事项

1. **测试环境**：建议在虚拟机中测试
2. **版本兼容**：针对当前360版本优化
3. **持续更新**：需要根据360更新调整技术
4. **合规使用**：仅用于合法的安全测试

## 后续优化建议

1. **动态配置**：根据环境自动选择最佳策略
2. **签名绕过**：添加代码签名相关技术
3. **行为分析对抗**：增强反行为分析能力
4. **云查杀绕过**：针对云端检测的优化

通过这些优化，项目成功解决了360安全软件的检测问题，为Cobalt Strike的正常使用提供了可靠的技术支持。
