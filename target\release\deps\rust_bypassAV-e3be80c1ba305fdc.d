C:\Users\<USER>\Desktop\rust_bypass1\target\release\deps\rust_bypassAV-e3be80c1ba305fdc.d: src\lib.rs src\config\mod.rs src\config\defense_config.rs src\config\execution_config.rs src\config\memory_config.rs src\config\sandbox_config.rs src\config\loader.rs src\core\mod.rs src\core\memory.rs src\core\syscalls.rs src\core\execution.rs src\core\crypto.rs src\core\encrypted_memory.rs src\core\shellcode_loader.rs src\core\embedded.rs src\error\mod.rs src\utils\mod.rs src\utils\code_cave_scanner.rs src\utils\logger.rs src\utils\pe_parser.rs src\utils\random.rs src\utils\winapi.rs src\utils\winapi_utils.rs src\defense\mod.rs src\defense\amsi.rs src\defense\cmdline.rs src\defense\etw.rs src\defense\identity.rs src\defense\ppid.rs src\defense\sandbox.rs src\defense\evasion_manager.rs src\orchestrator.rs src\constants.rs C:\Users\<USER>\Desktop\rust_bypass1\target\release\build\rust-bypassAV-5fcd884c8dd68e37\out/embedded_config.json C:\Users\<USER>\Desktop\rust_bypass1\target\release\build\rust-bypassAV-5fcd884c8dd68e37\out/embedded_shellcode.rs

C:\Users\<USER>\Desktop\rust_bypass1\target\release\deps\librust_bypassAV-e3be80c1ba305fdc.rlib: src\lib.rs src\config\mod.rs src\config\defense_config.rs src\config\execution_config.rs src\config\memory_config.rs src\config\sandbox_config.rs src\config\loader.rs src\core\mod.rs src\core\memory.rs src\core\syscalls.rs src\core\execution.rs src\core\crypto.rs src\core\encrypted_memory.rs src\core\shellcode_loader.rs src\core\embedded.rs src\error\mod.rs src\utils\mod.rs src\utils\code_cave_scanner.rs src\utils\logger.rs src\utils\pe_parser.rs src\utils\random.rs src\utils\winapi.rs src\utils\winapi_utils.rs src\defense\mod.rs src\defense\amsi.rs src\defense\cmdline.rs src\defense\etw.rs src\defense\identity.rs src\defense\ppid.rs src\defense\sandbox.rs src\defense\evasion_manager.rs src\orchestrator.rs src\constants.rs C:\Users\<USER>\Desktop\rust_bypass1\target\release\build\rust-bypassAV-5fcd884c8dd68e37\out/embedded_config.json C:\Users\<USER>\Desktop\rust_bypass1\target\release\build\rust-bypassAV-5fcd884c8dd68e37\out/embedded_shellcode.rs

C:\Users\<USER>\Desktop\rust_bypass1\target\release\deps\librust_bypassAV-e3be80c1ba305fdc.rmeta: src\lib.rs src\config\mod.rs src\config\defense_config.rs src\config\execution_config.rs src\config\memory_config.rs src\config\sandbox_config.rs src\config\loader.rs src\core\mod.rs src\core\memory.rs src\core\syscalls.rs src\core\execution.rs src\core\crypto.rs src\core\encrypted_memory.rs src\core\shellcode_loader.rs src\core\embedded.rs src\error\mod.rs src\utils\mod.rs src\utils\code_cave_scanner.rs src\utils\logger.rs src\utils\pe_parser.rs src\utils\random.rs src\utils\winapi.rs src\utils\winapi_utils.rs src\defense\mod.rs src\defense\amsi.rs src\defense\cmdline.rs src\defense\etw.rs src\defense\identity.rs src\defense\ppid.rs src\defense\sandbox.rs src\defense\evasion_manager.rs src\orchestrator.rs src\constants.rs C:\Users\<USER>\Desktop\rust_bypass1\target\release\build\rust-bypassAV-5fcd884c8dd68e37\out/embedded_config.json C:\Users\<USER>\Desktop\rust_bypass1\target\release\build\rust-bypassAV-5fcd884c8dd68e37\out/embedded_shellcode.rs

src\lib.rs:
src\config\mod.rs:
src\config\defense_config.rs:
src\config\execution_config.rs:
src\config\memory_config.rs:
src\config\sandbox_config.rs:
src\config\loader.rs:
src\core\mod.rs:
src\core\memory.rs:
src\core\syscalls.rs:
src\core\execution.rs:
src\core\crypto.rs:
src\core\encrypted_memory.rs:
src\core\shellcode_loader.rs:
src\core\embedded.rs:
src\error\mod.rs:
src\utils\mod.rs:
src\utils\code_cave_scanner.rs:
src\utils\logger.rs:
src\utils\pe_parser.rs:
src\utils\random.rs:
src\utils\winapi.rs:
src\utils\winapi_utils.rs:
src\defense\mod.rs:
src\defense\amsi.rs:
src\defense\cmdline.rs:
src\defense\etw.rs:
src\defense\identity.rs:
src\defense\ppid.rs:
src\defense\sandbox.rs:
src\defense\evasion_manager.rs:
src\orchestrator.rs:
src\constants.rs:
C:\Users\<USER>\Desktop\rust_bypass1\target\release\build\rust-bypassAV-5fcd884c8dd68e37\out/embedded_config.json:
C:\Users\<USER>\Desktop\rust_bypass1\target\release\build\rust-bypassAV-5fcd884c8dd68e37\out/embedded_shellcode.rs:

# env-dep:OUT_DIR=C:\\Users\\<USER>\\Desktop\\rust_bypass1\\target\\release\\build\\rust-bypassAV-5fcd884c8dd68e37\\out
